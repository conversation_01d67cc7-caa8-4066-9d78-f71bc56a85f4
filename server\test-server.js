const http = require('http');

const server = http.createServer((req, res) => {
  // Set CORS headers
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');
  res.setHeader('Content-Type', 'application/json; charset=utf-8');

  if (req.method === 'OPTIONS') {
    res.writeHead(200);
    res.end();
    return;
  }

  const url = req.url;
  
  if (url === '/' || url === '/api') {
    res.writeHead(200);
    res.end(JSON.stringify({
      message: 'مرحبًا بك في خادم متجر الأثاث',
      status: 'working',
      endpoints: [
        'GET /api/products',
        'GET /api/categories'
      ]
    }));
  } else if (url === '/api/products') {
    res.writeHead(200);
    res.end(JSON.stringify({
      products: [
        {
          _id: '1',
          name: 'كرسي مكتب مريح',
          description: 'كرسي مكتب عالي الجودة مع دعم للظهر',
          price: 850,
          category: 'مكتب',
          stock: 15,
          images: [],
          rating: 4.5
        },
        {
          _id: '2',
          name: 'طاولة طعام خشبية',
          description: 'طاولة طعام من الخشب الطبيعي تتسع لـ 6 أشخاص',
          price: 1200,
          category: 'طعام',
          stock: 8,
          images: [],
          rating: 4.8
        }
      ],
      totalPages: 1,
      currentPage: 1,
      total: 2
    }));
  } else if (url === '/api/categories') {
    res.writeHead(200);
    res.end(JSON.stringify([
      { _id: '1', name: 'غرفة المعيشة', description: 'أثاث غرفة المعيشة' },
      { _id: '2', name: 'غرفة النوم', description: 'أثاث غرفة النوم' },
      { _id: '3', name: 'المكتب', description: 'أثاث المكتب' },
      { _id: '4', name: 'غرفة الطعام', description: 'أثاث غرفة الطعام' }
    ]));
  } else if (url === '/api/analytics/dashboard') {
    res.writeHead(200);
    res.end(JSON.stringify({
      totalProducts: 2,
      totalOrders: 25,
      totalCustomers: 150,
      totalRevenue: 45000,
      recentOrders: [],
      topProducts: []
    }));
  } else {
    res.writeHead(404);
    res.end(JSON.stringify({ message: 'المسار غير موجود' }));
  }
});

const PORT = 5000;
server.listen(PORT, () => {
  console.log(`🚀 الخادم يعمل على المنفذ ${PORT}`);
  console.log(`📱 يمكنك الوصول للخادم على: http://localhost:${PORT}`);
  console.log(`📊 API متاح على: http://localhost:${PORT}/api`);
});
