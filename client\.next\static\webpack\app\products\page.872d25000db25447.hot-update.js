"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/products/page",{

/***/ "(app-pages-browser)/./src/app/products/page.js":
/*!**********************************!*\
  !*** ./src/app/products/page.js ***!
  \**********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ ProductsPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/../node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/../node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\nfunction ProductsPage() {\n    _s();\n    const [products, setProducts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [categories, setCategories] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [selectedCategory, setSelectedCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [priceRange, setPriceRange] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        min: \"\",\n        max: \"\"\n    });\n    const [currentPage, setCurrentPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [totalPages, setTotalPages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [showFilters, setShowFilters] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { addToCart } = useCart();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        fetchCategories();\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        fetchProducts();\n    }, [\n        searchTerm,\n        selectedCategory,\n        priceRange,\n        currentPage\n    ]);\n    const fetchCategories = async ()=>{\n        try {\n            const response = await api.get(\"/categories\");\n            setCategories(response.data);\n        } catch (error) {\n            console.error(\"Error fetching categories:\", error);\n        }\n    };\n    const fetchProducts = async ()=>{\n        setLoading(true);\n        try {\n            const params = new URLSearchParams({\n                page: currentPage,\n                limit: 12\n            });\n            if (searchTerm) params.append(\"search\", searchTerm);\n            if (selectedCategory) params.append(\"category\", selectedCategory);\n            if (priceRange.min) params.append(\"minPrice\", priceRange.min);\n            if (priceRange.max) params.append(\"maxPrice\", priceRange.max);\n            const response = await api.get(\"/products?\".concat(params));\n            setProducts(response.data.products || []);\n            setTotalPages(response.data.totalPages || 1);\n        } catch (error) {\n            console.error(\"Error fetching products:\", error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleSearch = (e)=>{\n        e.preventDefault();\n        setCurrentPage(1);\n        fetchProducts();\n    };\n    const handleAddToCart = (product)=>{\n        addToCart(product, 1);\n    };\n    const clearFilters = ()=>{\n        setSearchTerm(\"\");\n        setSelectedCategory(\"\");\n        setPriceRange({\n            min: \"\",\n            max: \"\"\n        });\n        setCurrentPage(1);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"container mx-auto px-4 py-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-3xl font-bold mb-4\",\n                        children: \"جميع المنتجات\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\d1\\\\client\\\\src\\\\app\\\\products\\\\page.js\",\n                        lineNumber: 79,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                        onSubmit: handleSearch,\n                        className: \"flex gap-2 mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        placeholder: \"ابحث عن المنتجات...\",\n                                        value: searchTerm,\n                                        onChange: (e)=>setSearchTerm(e.target.value),\n                                        className: \"form-input pl-10\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\d1\\\\client\\\\src\\\\app\\\\products\\\\page.js\",\n                                        lineNumber: 84,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FiSearch, {\n                                        className: \"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\d1\\\\client\\\\src\\\\app\\\\products\\\\page.js\",\n                                        lineNumber: 91,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\d1\\\\client\\\\src\\\\app\\\\products\\\\page.js\",\n                                lineNumber: 83,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"submit\",\n                                className: \"btn btn-primary\",\n                                children: \"بحث\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\d1\\\\client\\\\src\\\\app\\\\products\\\\page.js\",\n                                lineNumber: 93,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"button\",\n                                onClick: ()=>setShowFilters(!showFilters),\n                                className: \"btn btn-secondary md:hidden\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FiFilter, {}, void 0, false, {\n                                    fileName: \"E:\\\\d1\\\\client\\\\src\\\\app\\\\products\\\\page.js\",\n                                    lineNumber: 101,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\d1\\\\client\\\\src\\\\app\\\\products\\\\page.js\",\n                                lineNumber: 96,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\d1\\\\client\\\\src\\\\app\\\\products\\\\page.js\",\n                        lineNumber: 82,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\d1\\\\client\\\\src\\\\app\\\\products\\\\page.js\",\n                lineNumber: 78,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col md:flex-row gap-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"md:w-64 \".concat(showFilters ? \"block\" : \"hidden md:block\"),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white p-6 rounded-lg shadow-md\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between items-center mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold\",\n                                            children: \"الفلاتر\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\d1\\\\client\\\\src\\\\app\\\\products\\\\page.js\",\n                                            lineNumber: 111,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: clearFilters,\n                                            className: \"text-blue-600 text-sm hover:underline\",\n                                            children: \"مسح الكل\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\d1\\\\client\\\\src\\\\app\\\\products\\\\page.js\",\n                                            lineNumber: 112,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\d1\\\\client\\\\src\\\\app\\\\products\\\\page.js\",\n                                    lineNumber: 110,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"form-label\",\n                                            children: \"الفئة\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\d1\\\\client\\\\src\\\\app\\\\products\\\\page.js\",\n                                            lineNumber: 122,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                            value: selectedCategory,\n                                            onChange: (e)=>{\n                                                setSelectedCategory(e.target.value);\n                                                setCurrentPage(1);\n                                            },\n                                            className: \"form-select\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"\",\n                                                    children: \"جميع الفئات\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\d1\\\\client\\\\src\\\\app\\\\products\\\\page.js\",\n                                                    lineNumber: 131,\n                                                    columnNumber: 17\n                                                }, this),\n                                                categories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: category._id,\n                                                        children: category.name\n                                                    }, category._id, false, {\n                                                        fileName: \"E:\\\\d1\\\\client\\\\src\\\\app\\\\products\\\\page.js\",\n                                                        lineNumber: 133,\n                                                        columnNumber: 19\n                                                    }, this))\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\d1\\\\client\\\\src\\\\app\\\\products\\\\page.js\",\n                                            lineNumber: 123,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\d1\\\\client\\\\src\\\\app\\\\products\\\\page.js\",\n                                    lineNumber: 121,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"form-label\",\n                                            children: \"نطاق السعر\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\d1\\\\client\\\\src\\\\app\\\\products\\\\page.js\",\n                                            lineNumber: 142,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"number\",\n                                                    placeholder: \"من\",\n                                                    value: priceRange.min,\n                                                    onChange: (e)=>{\n                                                        setPriceRange((prev)=>({\n                                                                ...prev,\n                                                                min: e.target.value\n                                                            }));\n                                                        setCurrentPage(1);\n                                                    },\n                                                    className: \"form-input\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\d1\\\\client\\\\src\\\\app\\\\products\\\\page.js\",\n                                                    lineNumber: 144,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"number\",\n                                                    placeholder: \"إلى\",\n                                                    value: priceRange.max,\n                                                    onChange: (e)=>{\n                                                        setPriceRange((prev)=>({\n                                                                ...prev,\n                                                                max: e.target.value\n                                                            }));\n                                                        setCurrentPage(1);\n                                                    },\n                                                    className: \"form-input\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\d1\\\\client\\\\src\\\\app\\\\products\\\\page.js\",\n                                                    lineNumber: 154,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\d1\\\\client\\\\src\\\\app\\\\products\\\\page.js\",\n                                            lineNumber: 143,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\d1\\\\client\\\\src\\\\app\\\\products\\\\page.js\",\n                                    lineNumber: 141,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\d1\\\\client\\\\src\\\\app\\\\products\\\\page.js\",\n                            lineNumber: 109,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\d1\\\\client\\\\src\\\\app\\\\products\\\\page.js\",\n                        lineNumber: 108,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1\",\n                        children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-center items-center h-64\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"spinner border-blue-600\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\d1\\\\client\\\\src\\\\app\\\\products\\\\page.js\",\n                                lineNumber: 173,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\d1\\\\client\\\\src\\\\app\\\\products\\\\page.js\",\n                            lineNumber: 172,\n                            columnNumber: 13\n                        }, this) : products.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center py-12\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-500 text-lg\",\n                                children: \"لا توجد منتجات متاحة\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\d1\\\\client\\\\src\\\\app\\\\products\\\\page.js\",\n                                lineNumber: 177,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\d1\\\\client\\\\src\\\\app\\\\products\\\\page.js\",\n                            lineNumber: 176,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"product-grid\",\n                                    children: products.map((product)=>{\n                                        var _product_images;\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"card group\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative overflow-hidden\",\n                                                    children: [\n                                                        ((_product_images = product.images) === null || _product_images === void 0 ? void 0 : _product_images[0]) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Image, {\n                                                            src: product.images[0],\n                                                            alt: product.name,\n                                                            width: 300,\n                                                            height: 250,\n                                                            className: \"w-full h-64 object-cover group-hover:scale-105 transition-transform\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\d1\\\\client\\\\src\\\\app\\\\products\\\\page.js\",\n                                                            lineNumber: 186,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all flex items-center justify-center\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>handleAddToCart(product),\n                                                                className: \"bg-blue-600 text-white p-3 rounded-full opacity-0 group-hover:opacity-100 transform translate-y-4 group-hover:translate-y-0 transition-all\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FiShoppingCart, {\n                                                                    size: 20\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\d1\\\\client\\\\src\\\\app\\\\products\\\\page.js\",\n                                                                    lineNumber: 199,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\d1\\\\client\\\\src\\\\app\\\\products\\\\page.js\",\n                                                                lineNumber: 195,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\d1\\\\client\\\\src\\\\app\\\\products\\\\page.js\",\n                                                            lineNumber: 194,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\d1\\\\client\\\\src\\\\app\\\\products\\\\page.js\",\n                                                    lineNumber: 184,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"card-body\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"font-semibold text-lg mb-2 line-clamp-2\",\n                                                            children: product.name\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\d1\\\\client\\\\src\\\\app\\\\products\\\\page.js\",\n                                                            lineNumber: 205,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-600 text-sm mb-3 line-clamp-2\",\n                                                            children: product.description\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\d1\\\\client\\\\src\\\\app\\\\products\\\\page.js\",\n                                                            lineNumber: 206,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between mb-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center text-yellow-400\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FiStar, {\n                                                                                className: \"fill-current\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\d1\\\\client\\\\src\\\\app\\\\products\\\\page.js\",\n                                                                                lineNumber: 211,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-gray-600 text-sm mr-1\",\n                                                                                children: product.rating ? product.rating.toFixed(1) : \"0.0\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\d1\\\\client\\\\src\\\\app\\\\products\\\\page.js\",\n                                                                                lineNumber: 212,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"E:\\\\d1\\\\client\\\\src\\\\app\\\\products\\\\page.js\",\n                                                                        lineNumber: 210,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\d1\\\\client\\\\src\\\\app\\\\products\\\\page.js\",\n                                                                    lineNumber: 209,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-xl font-bold text-blue-600\",\n                                                                    children: [\n                                                                        product.price.toLocaleString(),\n                                                                        \" ر.س\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\d1\\\\client\\\\src\\\\app\\\\products\\\\page.js\",\n                                                                    lineNumber: 217,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\d1\\\\client\\\\src\\\\app\\\\products\\\\page.js\",\n                                                            lineNumber: 208,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Link, {\n                                                            href: \"/products/\".concat(product._id),\n                                                            className: \"btn btn-outline w-full\",\n                                                            children: \"عرض التفاصيل\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\d1\\\\client\\\\src\\\\app\\\\products\\\\page.js\",\n                                                            lineNumber: 222,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\d1\\\\client\\\\src\\\\app\\\\products\\\\page.js\",\n                                                    lineNumber: 204,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, product._id, true, {\n                                            fileName: \"E:\\\\d1\\\\client\\\\src\\\\app\\\\products\\\\page.js\",\n                                            lineNumber: 183,\n                                            columnNumber: 19\n                                        }, this);\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\d1\\\\client\\\\src\\\\app\\\\products\\\\page.js\",\n                                    lineNumber: 181,\n                                    columnNumber: 15\n                                }, this),\n                                totalPages > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-center mt-8\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex space-x-2 space-x-reverse\",\n                                        children: Array.from({\n                                            length: totalPages\n                                        }, (_, i)=>i + 1).map((page)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setCurrentPage(page),\n                                                className: \"px-4 py-2 rounded-lg \".concat(currentPage === page ? \"bg-blue-600 text-white\" : \"bg-gray-200 text-gray-700 hover:bg-gray-300\"),\n                                                children: page\n                                            }, page, false, {\n                                                fileName: \"E:\\\\d1\\\\client\\\\src\\\\app\\\\products\\\\page.js\",\n                                                lineNumber: 238,\n                                                columnNumber: 23\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\d1\\\\client\\\\src\\\\app\\\\products\\\\page.js\",\n                                        lineNumber: 236,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\d1\\\\client\\\\src\\\\app\\\\products\\\\page.js\",\n                                    lineNumber: 235,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\d1\\\\client\\\\src\\\\app\\\\products\\\\page.js\",\n                        lineNumber: 170,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\d1\\\\client\\\\src\\\\app\\\\products\\\\page.js\",\n                lineNumber: 106,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\d1\\\\client\\\\src\\\\app\\\\products\\\\page.js\",\n        lineNumber: 76,\n        columnNumber: 5\n    }, this);\n}\n_s(ProductsPage, \"i0AE++Uni27JTxIIAer4bVywPrs=\", true);\n_c = ProductsPage;\nvar _c;\n$RefreshReg$(_c, \"ProductsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/products/page.js\n"));

/***/ })

});