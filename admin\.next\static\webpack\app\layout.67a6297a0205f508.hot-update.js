"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony default export */ __webpack_exports__[\"default\"] = (\"eeb8863ec212\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6IjtBQUFBLCtEQUFlLGNBQWM7QUFDN0IsSUFBSSxJQUFVLElBQUksaUJBQWlCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL3NyYy9hcHAvZ2xvYmFscy5jc3M/NGRlYyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcImVlYjg4NjNlYzIxMlwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/globals.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/AdminLayout.js":
/*!***************************************!*\
  !*** ./src/components/AdminLayout.js ***!
  \***************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ AdminLayout; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/../node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/../node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/../node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/../node_modules/next/dist/api/link.js\");\n/* harmony import */ var _context_AuthContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../context/AuthContext */ \"(app-pages-browser)/./src/context/AuthContext.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n// Using simple text icons instead of react-icons\n\nfunction AdminLayout(param) {\n    let { children } = param;\n    _s();\n    const [sidebarOpen, setSidebarOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { user, logout, isAuthenticated, loading } = (0,_context_AuthContext__WEBPACK_IMPORTED_MODULE_4__.useAuth)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!loading && !isAuthenticated) {\n            router.push(\"/login\");\n        }\n    }, [\n        isAuthenticated,\n        loading,\n        router\n    ]);\n    const handleLogout = ()=>{\n        logout();\n        router.push(\"/login\");\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex justify-center items-center min-h-screen\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"spinner border-blue-600\"\n            }, void 0, false, {\n                fileName: \"E:\\\\d1\\\\admin\\\\src\\\\components\\\\AdminLayout.js\",\n                lineNumber: 28,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"E:\\\\d1\\\\admin\\\\src\\\\components\\\\AdminLayout.js\",\n            lineNumber: 27,\n            columnNumber: 7\n        }, this);\n    }\n    if (!isAuthenticated) {\n        return null;\n    }\n    const menuItems = [\n        {\n            href: \"/\",\n            icon: \"\\uD83C\\uDFE0\",\n            label: \"الرئيسية\"\n        },\n        {\n            href: \"/products\",\n            icon: \"\\uD83D\\uDCE6\",\n            label: \"المنتجات\"\n        },\n        {\n            href: \"/orders\",\n            icon: \"\\uD83D\\uDED2\",\n            label: \"الطلبات\"\n        },\n        {\n            href: \"/customers\",\n            icon: \"\\uD83D\\uDC65\",\n            label: \"العملاء\"\n        },\n        {\n            href: \"/analytics\",\n            icon: \"\\uD83D\\uDCCA\",\n            label: \"التقارير\"\n        },\n        {\n            href: \"/settings\",\n            icon: \"⚙️\",\n            label: \"الإعدادات\"\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex h-screen bg-gray-100\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"sidebar \".concat(sidebarOpen ? \"open\" : \"closed\"),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-xl font-bold text-white\",\n                            children: \"لوحة التحكم\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\d1\\\\admin\\\\src\\\\components\\\\AdminLayout.js\",\n                            lineNumber: 51,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\d1\\\\admin\\\\src\\\\components\\\\AdminLayout.js\",\n                        lineNumber: 50,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                        className: \"mt-6\",\n                        children: menuItems.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                href: item.href,\n                                className: \"flex items-center px-6 py-3 text-gray-300 hover:bg-gray-700 hover:text-white transition-colors\",\n                                onClick: ()=>setSidebarOpen(false),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"ml-3 text-xl\",\n                                        children: item.icon\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\d1\\\\admin\\\\src\\\\components\\\\AdminLayout.js\",\n                                        lineNumber: 62,\n                                        columnNumber: 15\n                                    }, this),\n                                    item.label\n                                ]\n                            }, item.href, true, {\n                                fileName: \"E:\\\\d1\\\\admin\\\\src\\\\components\\\\AdminLayout.js\",\n                                lineNumber: 56,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"E:\\\\d1\\\\admin\\\\src\\\\components\\\\AdminLayout.js\",\n                        lineNumber: 54,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute bottom-0 w-full p-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: handleLogout,\n                            className: \"flex items-center w-full px-4 py-2 text-gray-300 hover:bg-gray-700 hover:text-white transition-colors rounded-lg\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"ml-3 text-xl\",\n                                    children: \"\\uD83D\\uDEAA\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\d1\\\\admin\\\\src\\\\components\\\\AdminLayout.js\",\n                                    lineNumber: 73,\n                                    columnNumber: 13\n                                }, this),\n                                \"تسجيل الخروج\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\d1\\\\admin\\\\src\\\\components\\\\AdminLayout.js\",\n                            lineNumber: 69,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\d1\\\\admin\\\\src\\\\components\\\\AdminLayout.js\",\n                        lineNumber: 68,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\d1\\\\admin\\\\src\\\\components\\\\AdminLayout.js\",\n                lineNumber: 49,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 flex flex-col overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                        className: \"bg-white shadow-sm border-b border-gray-200\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between px-6 py-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setSidebarOpen(!sidebarOpen),\n                                            className: \"md:hidden p-2 rounded-lg text-gray-600 hover:bg-gray-100\",\n                                            children: sidebarOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FiX, {\n                                                size: 24\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\d1\\\\admin\\\\src\\\\components\\\\AdminLayout.js\",\n                                                lineNumber: 89,\n                                                columnNumber: 32\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FiMenu, {\n                                                size: 24\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\d1\\\\admin\\\\src\\\\components\\\\AdminLayout.js\",\n                                                lineNumber: 89,\n                                                columnNumber: 52\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\d1\\\\admin\\\\src\\\\components\\\\AdminLayout.js\",\n                                            lineNumber: 85,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                            className: \"text-xl font-semibold text-gray-800 mr-4\",\n                                            children: [\n                                                \"مرحباً، \",\n                                                user === null || user === void 0 ? void 0 : user.name\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\d1\\\\admin\\\\src\\\\components\\\\AdminLayout.js\",\n                                            lineNumber: 91,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\d1\\\\admin\\\\src\\\\components\\\\AdminLayout.js\",\n                                    lineNumber: 84,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-4 space-x-reverse\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm text-gray-600\",\n                                        children: user === null || user === void 0 ? void 0 : user.email\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\d1\\\\admin\\\\src\\\\components\\\\AdminLayout.js\",\n                                        lineNumber: 97,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\d1\\\\admin\\\\src\\\\components\\\\AdminLayout.js\",\n                                    lineNumber: 96,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\d1\\\\admin\\\\src\\\\components\\\\AdminLayout.js\",\n                            lineNumber: 83,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\d1\\\\admin\\\\src\\\\components\\\\AdminLayout.js\",\n                        lineNumber: 82,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"flex-1 overflow-y-auto p-6\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"E:\\\\d1\\\\admin\\\\src\\\\components\\\\AdminLayout.js\",\n                        lineNumber: 103,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\d1\\\\admin\\\\src\\\\components\\\\AdminLayout.js\",\n                lineNumber: 80,\n                columnNumber: 7\n            }, this),\n            sidebarOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black bg-opacity-50 z-30 md:hidden\",\n                onClick: ()=>setSidebarOpen(false)\n            }, void 0, false, {\n                fileName: \"E:\\\\d1\\\\admin\\\\src\\\\components\\\\AdminLayout.js\",\n                lineNumber: 110,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\d1\\\\admin\\\\src\\\\components\\\\AdminLayout.js\",\n        lineNumber: 47,\n        columnNumber: 5\n    }, this);\n}\n_s(AdminLayout, \"/p8qhRqEdaqVR7/WhqVCqNXBZIg=\", false, function() {\n    return [\n        _context_AuthContext__WEBPACK_IMPORTED_MODULE_4__.useAuth,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = AdminLayout;\nvar _c;\n$RefreshReg$(_c, \"AdminLayout\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/AdminLayout.js\n"));

/***/ })

});