'use client';

import { useState } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { FiMinus, FiPlus, FiTrash2, FiShoppingBag } from 'react-icons/fi';
import { useCart } from '../../context/CartContext';
import { useAuth } from '../../context/AuthContext';

export default function CartPage() {
  const { cartItems, updateQuantity, removeFromCart, getCartTotal, clearCart } = useCart();
  const { isAuthenticated } = useAuth();
  const [isClearing, setIsClearing] = useState(false);

  const handleQuantityChange = (productId, newQuantity) => {
    if (newQuantity <= 0) {
      removeFromCart(productId);
    } else {
      updateQuantity(productId, newQuantity);
    }
  };

  const handleClearCart = () => {
    setIsClearing(true);
    setTimeout(() => {
      clearCart();
      setIsClearing(false);
    }, 500);
  };

  if (cartItems.length === 0) {
    return (
      <div className="container mx-auto px-4 py-16 text-center">
        <div className="max-w-md mx-auto">
          <FiShoppingBag size={80} className="mx-auto text-gray-300 mb-6" />
          <h1 className="text-2xl font-bold text-gray-600 mb-4">السلة فارغة</h1>
          <p className="text-gray-500 mb-8">لم تقم بإضافة أي منتجات إلى السلة بعد</p>
          <Link href="/products" className="btn btn-primary">
            تسوق الآن
          </Link>
        </div>
      </div>
    );
  }

  const total = getCartTotal();
  const shipping = total > 500 ? 0 : 50; // Free shipping over 500 SAR
  const finalTotal = total + shipping;

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex justify-between items-center mb-8">
        <h1 className="text-3xl font-bold">سلة التسوق</h1>
        <button
          onClick={handleClearCart}
          disabled={isClearing}
          className="btn btn-danger"
        >
          {isClearing ? 'جاري التفريغ...' : 'تفريغ السلة'}
        </button>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Cart Items */}
        <div className="lg:col-span-2">
          <div className="space-y-4">
            {cartItems.map((item) => (
              <div key={item.product._id} className="bg-white p-6 rounded-lg shadow-md">
                <div className="flex items-center space-x-4 space-x-reverse">
                  {/* Product Image */}
                  <div className="flex-shrink-0">
                    {item.product.images?.[0] && (
                      <Image
                        src={item.product.images[0]}
                        alt={item.product.name}
                        width={100}
                        height={100}
                        className="w-24 h-24 object-cover rounded-lg"
                      />
                    )}
                  </div>

                  {/* Product Info */}
                  <div className="flex-1">
                    <h3 className="text-lg font-semibold mb-2">
                      <Link
                        href={`/products/${item.product._id}`}
                        className="hover:text-blue-600 transition-colors"
                      >
                        {item.product.name}
                      </Link>
                    </h3>
                    <p className="text-gray-600 text-sm mb-2 line-clamp-2">
                      {item.product.description}
                    </p>
                    <div className="text-lg font-bold text-blue-600">
                      {item.product.price.toLocaleString()} ر.س
                    </div>
                  </div>

                  {/* Quantity Controls */}
                  <div className="flex items-center space-x-2 space-x-reverse">
                    <button
                      onClick={() => handleQuantityChange(item.product._id, item.quantity - 1)}
                      className="p-2 border border-gray-300 rounded-lg hover:bg-gray-100"
                    >
                      <FiMinus size={16} />
                    </button>
                    <span className="px-4 py-2 border border-gray-300 rounded-lg min-w-[60px] text-center">
                      {item.quantity}
                    </span>
                    <button
                      onClick={() => handleQuantityChange(item.product._id, item.quantity + 1)}
                      className="p-2 border border-gray-300 rounded-lg hover:bg-gray-100"
                    >
                      <FiPlus size={16} />
                    </button>
                  </div>

                  {/* Item Total */}
                  <div className="text-lg font-bold">
                    {(item.product.price * item.quantity).toLocaleString()} ر.س
                  </div>

                  {/* Remove Button */}
                  <button
                    onClick={() => removeFromCart(item.product._id)}
                    className="p-2 text-red-600 hover:bg-red-50 rounded-lg transition-colors"
                  >
                    <FiTrash2 size={20} />
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Order Summary */}
        <div className="lg:col-span-1">
          <div className="bg-white p-6 rounded-lg shadow-md sticky top-8">
            <h2 className="text-xl font-bold mb-6">ملخص الطلب</h2>
            
            <div className="space-y-4 mb-6">
              <div className="flex justify-between">
                <span>المجموع الفرعي:</span>
                <span>{total.toLocaleString()} ر.س</span>
              </div>
              
              <div className="flex justify-between">
                <span>الشحن:</span>
                <span className={shipping === 0 ? 'text-green-600' : ''}>
                  {shipping === 0 ? 'مجاني' : `${shipping} ر.س`}
                </span>
              </div>
              
              {shipping === 0 && total <= 500 && (
                <p className="text-sm text-green-600">
                  🎉 تهانينا! حصلت على شحن مجاني
                </p>
              )}
              
              {shipping > 0 && (
                <p className="text-sm text-gray-600">
                  أضف {(500 - total).toLocaleString()} ر.س للحصول على شحن مجاني
                </p>
              )}
              
              <div className="border-t pt-4">
                <div className="flex justify-between text-lg font-bold">
                  <span>المجموع الكلي:</span>
                  <span className="text-blue-600">{finalTotal.toLocaleString()} ر.س</span>
                </div>
              </div>
            </div>

            {/* Checkout Buttons */}
            <div className="space-y-3">
              {isAuthenticated ? (
                <Link href="/checkout" className="btn btn-primary w-full text-center">
                  إتمام الطلب
                </Link>
              ) : (
                <div className="space-y-2">
                  <Link href="/login?redirect=/checkout" className="btn btn-primary w-full text-center">
                    تسجيل الدخول للمتابعة
                  </Link>
                  <p className="text-sm text-gray-600 text-center">
                    أو{' '}
                    <Link href="/register" className="text-blue-600 hover:underline">
                      إنشاء حساب جديد
                    </Link>
                  </p>
                </div>
              )}
              
              <Link href="/products" className="btn btn-secondary w-full text-center">
                متابعة التسوق
              </Link>
            </div>

            {/* Security Badge */}
            <div className="mt-6 pt-6 border-t text-center">
              <div className="flex items-center justify-center space-x-2 space-x-reverse text-sm text-gray-600">
                <span>🔒</span>
                <span>دفع آمن ومضمون</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
