{"version": 3, "sources": ["../../../src/server/web/utils.ts"], "names": ["fromNodeOutgoingHttpHeaders", "normalizeNextQueryParam", "splitCookiesString", "toNodeOutgoingHttpHeaders", "validateURL", "nodeHeaders", "headers", "Headers", "key", "value", "Object", "entries", "values", "Array", "isArray", "v", "toString", "append", "cookiesString", "cookiesStrings", "pos", "start", "ch", "lastComma", "nextStart", "cookiesSeparatorFound", "skipWhitespace", "length", "test", "char<PERSON>t", "notSpecialChar", "push", "substring", "cookies", "toLowerCase", "url", "String", "URL", "error", "Error", "cause", "onKeyNormalized", "prefixes", "NEXT_QUERY_PARAM_PREFIX", "NEXT_INTERCEPTION_MARKER_PREFIX", "prefix", "startsWith", "normalizedKey"], "mappings": ";;;;;;;;;;;;;;;;;;IAegBA,2BAA2B;eAA3BA;;IA+IAC,uBAAuB;eAAvBA;;IAnHAC,kBAAkB;eAAlBA;;IAyEAC,yBAAyB;eAAzBA;;IAwBAC,WAAW;eAAXA;;;2BAxIT;AAWA,SAASJ,4BACdK,WAAgC;IAEhC,MAAMC,UAAU,IAAIC;IACpB,KAAK,IAAI,CAACC,KAAKC,MAAM,IAAIC,OAAOC,OAAO,CAACN,aAAc;QACpD,MAAMO,SAASC,MAAMC,OAAO,CAACL,SAASA,QAAQ;YAACA;SAAM;QACrD,KAAK,IAAIM,KAAKH,OAAQ;YACpB,IAAI,OAAOG,MAAM,aAAa;YAC9B,IAAI,OAAOA,MAAM,UAAU;gBACzBA,IAAIA,EAAEC,QAAQ;YAChB;YAEAV,QAAQW,MAAM,CAACT,KAAKO;QACtB;IACF;IACA,OAAOT;AACT;AAYO,SAASJ,mBAAmBgB,aAAqB;IACtD,IAAIC,iBAAiB,EAAE;IACvB,IAAIC,MAAM;IACV,IAAIC;IACJ,IAAIC;IACJ,IAAIC;IACJ,IAAIC;IACJ,IAAIC;IAEJ,SAASC;QACP,MAAON,MAAMF,cAAcS,MAAM,IAAI,KAAKC,IAAI,CAACV,cAAcW,MAAM,CAACT,MAAO;YACzEA,OAAO;QACT;QACA,OAAOA,MAAMF,cAAcS,MAAM;IACnC;IAEA,SAASG;QACPR,KAAKJ,cAAcW,MAAM,CAACT;QAE1B,OAAOE,OAAO,OAAOA,OAAO,OAAOA,OAAO;IAC5C;IAEA,MAAOF,MAAMF,cAAcS,MAAM,CAAE;QACjCN,QAAQD;QACRK,wBAAwB;QAExB,MAAOC,iBAAkB;YACvBJ,KAAKJ,cAAcW,MAAM,CAACT;YAC1B,IAAIE,OAAO,KAAK;gBACd,uEAAuE;gBACvEC,YAAYH;gBACZA,OAAO;gBAEPM;gBACAF,YAAYJ;gBAEZ,MAAOA,MAAMF,cAAcS,MAAM,IAAIG,iBAAkB;oBACrDV,OAAO;gBACT;gBAEA,8BAA8B;gBAC9B,IAAIA,MAAMF,cAAcS,MAAM,IAAIT,cAAcW,MAAM,CAACT,SAAS,KAAK;oBACnE,6BAA6B;oBAC7BK,wBAAwB;oBACxB,2DAA2D;oBAC3DL,MAAMI;oBACNL,eAAeY,IAAI,CAACb,cAAcc,SAAS,CAACX,OAAOE;oBACnDF,QAAQD;gBACV,OAAO;oBACL,uCAAuC;oBACvC,8BAA8B;oBAC9BA,MAAMG,YAAY;gBACpB;YACF,OAAO;gBACLH,OAAO;YACT;QACF;QAEA,IAAI,CAACK,yBAAyBL,OAAOF,cAAcS,MAAM,EAAE;YACzDR,eAAeY,IAAI,CAACb,cAAcc,SAAS,CAACX,OAAOH,cAAcS,MAAM;QACzE;IACF;IAEA,OAAOR;AACT;AASO,SAAShB,0BACdG,OAAgB;IAEhB,MAAMD,cAAmC,CAAC;IAC1C,MAAM4B,UAAoB,EAAE;IAC5B,IAAI3B,SAAS;QACX,KAAK,MAAM,CAACE,KAAKC,MAAM,IAAIH,QAAQK,OAAO,GAAI;YAC5C,IAAIH,IAAI0B,WAAW,OAAO,cAAc;gBACtC,mEAAmE;gBACnE,kEAAkE;gBAClE,gCAAgC;gBAChCD,QAAQF,IAAI,IAAI7B,mBAAmBO;gBACnCJ,WAAW,CAACG,IAAI,GAAGyB,QAAQN,MAAM,KAAK,IAAIM,OAAO,CAAC,EAAE,GAAGA;YACzD,OAAO;gBACL5B,WAAW,CAACG,IAAI,GAAGC;YACrB;QACF;IACF;IACA,OAAOJ;AACT;AAKO,SAASD,YAAY+B,GAAiB;IAC3C,IAAI;QACF,OAAOC,OAAO,IAAIC,IAAID,OAAOD;IAC/B,EAAE,OAAOG,OAAY;QACnB,MAAM,IAAIC,MACR,CAAC,kBAAkB,EAAEH,OACnBD,KACA,4FAA4F,CAAC,EAC/F;YAAEK,OAAOF;QAAM;IAEnB;AACF;AAOO,SAASrC,wBACdO,GAAW,EACXiC,eAAgD;IAEhD,MAAMC,WAAW;QAACC,kCAAuB;QAAEC,0CAA+B;KAAC;IAC3E,KAAK,MAAMC,UAAUH,SAAU;QAC7B,IAAIlC,QAAQqC,UAAUrC,IAAIsC,UAAU,CAACD,SAAS;YAC5C,MAAME,gBAAgBvC,IAAIwB,SAAS,CAACa,OAAOlB,MAAM;YACjDc,gBAAgBM;QAClB;IACF;AACF"}