"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/page.js":
/*!*************************!*\
  !*** ./src/app/page.js ***!
  \*************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ AdminHome; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/../node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/../node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\nfunction AdminHome() {\n    _s();\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        totalProducts: 0,\n        totalOrders: 0,\n        totalCustomers: 0,\n        totalRevenue: 0,\n        recentOrders: [],\n        topProducts: []\n    });\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        fetchDashboardData();\n    }, []);\n    const fetchDashboardData = async ()=>{\n        try {\n            const response = await api.get(\"/analytics/dashboard\");\n            setStats(response.data);\n        } catch (error) {\n            console.error(\"Error fetching dashboard data:\", error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex justify-center items-center h-64\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"spinner border-blue-600\"\n            }, void 0, false, {\n                fileName: \"E:\\\\d1\\\\admin\\\\src\\\\app\\\\page.js\",\n                lineNumber: 34,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"E:\\\\d1\\\\admin\\\\src\\\\app\\\\page.js\",\n            lineNumber: 33,\n            columnNumber: 7\n        }, this);\n    }\n    const statCards = [\n        {\n            title: \"إجمالي المنتجات\",\n            value: stats.totalProducts,\n            icon: FiPackage,\n            color: \"bg-blue-500\"\n        },\n        {\n            title: \"إجمالي الطلبات\",\n            value: stats.totalOrders,\n            icon: FiShoppingCart,\n            color: \"bg-green-500\"\n        },\n        {\n            title: \"إجمالي العملاء\",\n            value: stats.totalCustomers,\n            icon: FiUsers,\n            color: \"bg-purple-500\"\n        },\n        {\n            title: \"إجمالي الإيرادات\",\n            value: \"\".concat(stats.totalRevenue.toLocaleString(), \" ر.س\"),\n            icon: FiDollarSign,\n            color: \"bg-yellow-500\"\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                className: \"text-3xl font-bold text-gray-800 mb-8\",\n                children: \"لوحة التحكم\"\n            }, void 0, false, {\n                fileName: \"E:\\\\d1\\\\admin\\\\src\\\\app\\\\page.js\",\n                lineNumber: 68,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"dashboard-grid mb-8\",\n                children: statCards.map((stat, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"stats-card\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm font-medium text-gray-600\",\n                                            children: stat.title\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\d1\\\\admin\\\\src\\\\app\\\\page.js\",\n                                            lineNumber: 76,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-2xl font-bold text-gray-900\",\n                                            children: stat.value\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\d1\\\\admin\\\\src\\\\app\\\\page.js\",\n                                            lineNumber: 77,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\d1\\\\admin\\\\src\\\\app\\\\page.js\",\n                                    lineNumber: 75,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"stats-card-icon \".concat(stat.color),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(stat.icon, {\n                                        size: 24\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\d1\\\\admin\\\\src\\\\app\\\\page.js\",\n                                        lineNumber: 80,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\d1\\\\admin\\\\src\\\\app\\\\page.js\",\n                                    lineNumber: 79,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\d1\\\\admin\\\\src\\\\app\\\\page.js\",\n                            lineNumber: 74,\n                            columnNumber: 13\n                        }, this)\n                    }, index, false, {\n                        fileName: \"E:\\\\d1\\\\admin\\\\src\\\\app\\\\page.js\",\n                        lineNumber: 73,\n                        columnNumber: 11\n                    }, this))\n            }, void 0, false, {\n                fileName: \"E:\\\\d1\\\\admin\\\\src\\\\app\\\\page.js\",\n                lineNumber: 71,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 lg:grid-cols-2 gap-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"card\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"card-header\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-lg font-semibold\",\n                                    children: \"الطلبات الأخيرة\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\d1\\\\admin\\\\src\\\\app\\\\page.js\",\n                                    lineNumber: 91,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\d1\\\\admin\\\\src\\\\app\\\\page.js\",\n                                lineNumber: 90,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"card-body\",\n                                children: stats.recentOrders.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: stats.recentOrders.map((order)=>{\n                                        var _order_customer;\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between p-3 bg-gray-50 rounded-lg\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"font-medium\",\n                                                            children: (_order_customer = order.customer) === null || _order_customer === void 0 ? void 0 : _order_customer.name\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\d1\\\\admin\\\\src\\\\app\\\\page.js\",\n                                                            lineNumber: 99,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-600\",\n                                                            children: [\n                                                                order.items.length,\n                                                                \" منتج - \",\n                                                                order.totalAmount.toLocaleString(),\n                                                                \" ر.س\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\d1\\\\admin\\\\src\\\\app\\\\page.js\",\n                                                            lineNumber: 100,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\d1\\\\admin\\\\src\\\\app\\\\page.js\",\n                                                    lineNumber: 98,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"px-2 py-1 text-xs rounded-full \".concat(order.status === \"delivered\" ? \"bg-green-100 text-green-800\" : order.status === \"pending\" ? \"bg-yellow-100 text-yellow-800\" : \"bg-blue-100 text-blue-800\"),\n                                                    children: order.status === \"delivered\" ? \"تم التسليم\" : order.status === \"pending\" ? \"في الانتظار\" : \"قيد المعالجة\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\d1\\\\admin\\\\src\\\\app\\\\page.js\",\n                                                    lineNumber: 104,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, order._id, true, {\n                                            fileName: \"E:\\\\d1\\\\admin\\\\src\\\\app\\\\page.js\",\n                                            lineNumber: 97,\n                                            columnNumber: 19\n                                        }, this);\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\d1\\\\admin\\\\src\\\\app\\\\page.js\",\n                                    lineNumber: 95,\n                                    columnNumber: 15\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-500 text-center py-4\",\n                                    children: \"لا توجد طلبات حديثة\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\d1\\\\admin\\\\src\\\\app\\\\page.js\",\n                                    lineNumber: 116,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\d1\\\\admin\\\\src\\\\app\\\\page.js\",\n                                lineNumber: 93,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\d1\\\\admin\\\\src\\\\app\\\\page.js\",\n                        lineNumber: 89,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"card\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"card-header\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-lg font-semibold\",\n                                    children: \"المنتجات الأكثر مبيعاً\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\d1\\\\admin\\\\src\\\\app\\\\page.js\",\n                                    lineNumber: 124,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\d1\\\\admin\\\\src\\\\app\\\\page.js\",\n                                lineNumber: 123,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"card-body\",\n                                children: stats.topProducts.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: stats.topProducts.map((item, index)=>{\n                                        var _item_product, _item_revenue;\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between p-3 bg-gray-50 rounded-lg\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"font-medium\",\n                                                            children: (_item_product = item.product) === null || _item_product === void 0 ? void 0 : _item_product.name\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\d1\\\\admin\\\\src\\\\app\\\\page.js\",\n                                                            lineNumber: 132,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-600\",\n                                                            children: [\n                                                                item.totalSold,\n                                                                \" قطعة مباعة\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\d1\\\\admin\\\\src\\\\app\\\\page.js\",\n                                                            lineNumber: 133,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\d1\\\\admin\\\\src\\\\app\\\\page.js\",\n                                                    lineNumber: 131,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-right\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"font-bold text-green-600\",\n                                                        children: [\n                                                            (_item_revenue = item.revenue) === null || _item_revenue === void 0 ? void 0 : _item_revenue.toLocaleString(),\n                                                            \" ر.س\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\d1\\\\admin\\\\src\\\\app\\\\page.js\",\n                                                        lineNumber: 138,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\d1\\\\admin\\\\src\\\\app\\\\page.js\",\n                                                    lineNumber: 137,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, index, true, {\n                                            fileName: \"E:\\\\d1\\\\admin\\\\src\\\\app\\\\page.js\",\n                                            lineNumber: 130,\n                                            columnNumber: 19\n                                        }, this);\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\d1\\\\admin\\\\src\\\\app\\\\page.js\",\n                                    lineNumber: 128,\n                                    columnNumber: 15\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-500 text-center py-4\",\n                                    children: \"لا توجد بيانات مبيعات\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\d1\\\\admin\\\\src\\\\app\\\\page.js\",\n                                    lineNumber: 146,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\d1\\\\admin\\\\src\\\\app\\\\page.js\",\n                                lineNumber: 126,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\d1\\\\admin\\\\src\\\\app\\\\page.js\",\n                        lineNumber: 122,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\d1\\\\admin\\\\src\\\\app\\\\page.js\",\n                lineNumber: 87,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\d1\\\\admin\\\\src\\\\app\\\\page.js\",\n        lineNumber: 67,\n        columnNumber: 5\n    }, this);\n}\n_s(AdminHome, \"yYtFbER8KXrK9zGyRg9Hc9Cj0bc=\");\n_c = AdminHome;\nvar _c;\n$RefreshReg$(_c, \"AdminHome\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/page.js\n"));

/***/ })

});