"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/products/page",{

/***/ "(app-pages-browser)/./src/app/products/page.js":
/*!**********************************!*\
  !*** ./src/app/products/page.js ***!
  \**********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ ProductsPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/../node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/../node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\nfunction ProductsPage() {\n    _s();\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [selectedCategory, setSelectedCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // Sample data\n    const products = [\n        {\n            _id: \"1\",\n            name: \"كرسي مكتب مريح\",\n            description: \"كرسي مكتب عالي الجودة مع دعم للظهر\",\n            price: 850,\n            category: \"مكتب\",\n            stock: 15,\n            rating: 4.5\n        },\n        {\n            _id: \"2\",\n            name: \"طاولة طعام خشبية\",\n            description: \"طاولة طعام من الخشب الطبيعي تتسع لـ 6 أشخاص\",\n            price: 1200,\n            category: \"طعام\",\n            stock: 8,\n            rating: 4.8\n        },\n        {\n            _id: \"3\",\n            name: \"سرير مزدوج\",\n            description: \"سرير مزدوج مريح من الخشب الطبيعي\",\n            price: 2500,\n            category: \"نوم\",\n            stock: 5,\n            rating: 4.7\n        },\n        {\n            _id: \"4\",\n            name: \"خزانة ملابس\",\n            description: \"خزانة ملابس واسعة بثلاث أبواب\",\n            price: 1800,\n            category: \"نوم\",\n            stock: 3,\n            rating: 4.6\n        }\n    ];\n    const categories = [\n        {\n            _id: \"1\",\n            name: \"غرفة المعيشة\"\n        },\n        {\n            _id: \"2\",\n            name: \"غرفة النوم\"\n        },\n        {\n            _id: \"3\",\n            name: \"المكتب\"\n        },\n        {\n            _id: \"4\",\n            name: \"غرفة الطعام\"\n        }\n    ];\n    const handleSearch = (e)=>{\n        e.preventDefault();\n    // Handle search logic here\n    };\n    const clearFilters = ()=>{\n        setSearchTerm(\"\");\n        setSelectedCategory(\"\");\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"container mx-auto px-4 py-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-3xl font-bold mb-4\",\n                        children: \"جميع المنتجات\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\d1\\\\client\\\\src\\\\app\\\\products\\\\page.js\",\n                        lineNumber: 71,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                        onSubmit: handleSearch,\n                        className: \"flex gap-2 mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        placeholder: \"ابحث عن المنتجات...\",\n                                        value: searchTerm,\n                                        onChange: (e)=>setSearchTerm(e.target.value),\n                                        className: \"form-input pl-10\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\d1\\\\client\\\\src\\\\app\\\\products\\\\page.js\",\n                                        lineNumber: 76,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FiSearch, {\n                                        className: \"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\d1\\\\client\\\\src\\\\app\\\\products\\\\page.js\",\n                                        lineNumber: 83,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\d1\\\\client\\\\src\\\\app\\\\products\\\\page.js\",\n                                lineNumber: 75,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"submit\",\n                                className: \"btn btn-primary\",\n                                children: \"بحث\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\d1\\\\client\\\\src\\\\app\\\\products\\\\page.js\",\n                                lineNumber: 85,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"button\",\n                                onClick: ()=>setShowFilters(!showFilters),\n                                className: \"btn btn-secondary md:hidden\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FiFilter, {}, void 0, false, {\n                                    fileName: \"E:\\\\d1\\\\client\\\\src\\\\app\\\\products\\\\page.js\",\n                                    lineNumber: 93,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\d1\\\\client\\\\src\\\\app\\\\products\\\\page.js\",\n                                lineNumber: 88,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\d1\\\\client\\\\src\\\\app\\\\products\\\\page.js\",\n                        lineNumber: 74,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\d1\\\\client\\\\src\\\\app\\\\products\\\\page.js\",\n                lineNumber: 70,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col md:flex-row gap-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"md:w-64 \".concat(showFilters ? \"block\" : \"hidden md:block\"),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white p-6 rounded-lg shadow-md\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between items-center mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold\",\n                                            children: \"الفلاتر\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\d1\\\\client\\\\src\\\\app\\\\products\\\\page.js\",\n                                            lineNumber: 103,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: clearFilters,\n                                            className: \"text-blue-600 text-sm hover:underline\",\n                                            children: \"مسح الكل\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\d1\\\\client\\\\src\\\\app\\\\products\\\\page.js\",\n                                            lineNumber: 104,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\d1\\\\client\\\\src\\\\app\\\\products\\\\page.js\",\n                                    lineNumber: 102,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"form-label\",\n                                            children: \"الفئة\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\d1\\\\client\\\\src\\\\app\\\\products\\\\page.js\",\n                                            lineNumber: 114,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                            value: selectedCategory,\n                                            onChange: (e)=>{\n                                                setSelectedCategory(e.target.value);\n                                                setCurrentPage(1);\n                                            },\n                                            className: \"form-select\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"\",\n                                                    children: \"جميع الفئات\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\d1\\\\client\\\\src\\\\app\\\\products\\\\page.js\",\n                                                    lineNumber: 123,\n                                                    columnNumber: 17\n                                                }, this),\n                                                categories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: category._id,\n                                                        children: category.name\n                                                    }, category._id, false, {\n                                                        fileName: \"E:\\\\d1\\\\client\\\\src\\\\app\\\\products\\\\page.js\",\n                                                        lineNumber: 125,\n                                                        columnNumber: 19\n                                                    }, this))\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\d1\\\\client\\\\src\\\\app\\\\products\\\\page.js\",\n                                            lineNumber: 115,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\d1\\\\client\\\\src\\\\app\\\\products\\\\page.js\",\n                                    lineNumber: 113,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"form-label\",\n                                            children: \"نطاق السعر\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\d1\\\\client\\\\src\\\\app\\\\products\\\\page.js\",\n                                            lineNumber: 134,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"number\",\n                                                    placeholder: \"من\",\n                                                    value: priceRange.min,\n                                                    onChange: (e)=>{\n                                                        setPriceRange((prev)=>({\n                                                                ...prev,\n                                                                min: e.target.value\n                                                            }));\n                                                        setCurrentPage(1);\n                                                    },\n                                                    className: \"form-input\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\d1\\\\client\\\\src\\\\app\\\\products\\\\page.js\",\n                                                    lineNumber: 136,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"number\",\n                                                    placeholder: \"إلى\",\n                                                    value: priceRange.max,\n                                                    onChange: (e)=>{\n                                                        setPriceRange((prev)=>({\n                                                                ...prev,\n                                                                max: e.target.value\n                                                            }));\n                                                        setCurrentPage(1);\n                                                    },\n                                                    className: \"form-input\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\d1\\\\client\\\\src\\\\app\\\\products\\\\page.js\",\n                                                    lineNumber: 146,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\d1\\\\client\\\\src\\\\app\\\\products\\\\page.js\",\n                                            lineNumber: 135,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\d1\\\\client\\\\src\\\\app\\\\products\\\\page.js\",\n                                    lineNumber: 133,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\d1\\\\client\\\\src\\\\app\\\\products\\\\page.js\",\n                            lineNumber: 101,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\d1\\\\client\\\\src\\\\app\\\\products\\\\page.js\",\n                        lineNumber: 100,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1\",\n                        children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-center items-center h-64\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"spinner border-blue-600\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\d1\\\\client\\\\src\\\\app\\\\products\\\\page.js\",\n                                lineNumber: 165,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\d1\\\\client\\\\src\\\\app\\\\products\\\\page.js\",\n                            lineNumber: 164,\n                            columnNumber: 13\n                        }, this) : products.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center py-12\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-500 text-lg\",\n                                children: \"لا توجد منتجات متاحة\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\d1\\\\client\\\\src\\\\app\\\\products\\\\page.js\",\n                                lineNumber: 169,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\d1\\\\client\\\\src\\\\app\\\\products\\\\page.js\",\n                            lineNumber: 168,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"product-grid\",\n                                    children: products.map((product)=>{\n                                        var _product_images;\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"card group\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative overflow-hidden\",\n                                                    children: [\n                                                        ((_product_images = product.images) === null || _product_images === void 0 ? void 0 : _product_images[0]) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Image, {\n                                                            src: product.images[0],\n                                                            alt: product.name,\n                                                            width: 300,\n                                                            height: 250,\n                                                            className: \"w-full h-64 object-cover group-hover:scale-105 transition-transform\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\d1\\\\client\\\\src\\\\app\\\\products\\\\page.js\",\n                                                            lineNumber: 178,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all flex items-center justify-center\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>handleAddToCart(product),\n                                                                className: \"bg-blue-600 text-white p-3 rounded-full opacity-0 group-hover:opacity-100 transform translate-y-4 group-hover:translate-y-0 transition-all\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FiShoppingCart, {\n                                                                    size: 20\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\d1\\\\client\\\\src\\\\app\\\\products\\\\page.js\",\n                                                                    lineNumber: 191,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\d1\\\\client\\\\src\\\\app\\\\products\\\\page.js\",\n                                                                lineNumber: 187,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\d1\\\\client\\\\src\\\\app\\\\products\\\\page.js\",\n                                                            lineNumber: 186,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\d1\\\\client\\\\src\\\\app\\\\products\\\\page.js\",\n                                                    lineNumber: 176,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"card-body\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"font-semibold text-lg mb-2 line-clamp-2\",\n                                                            children: product.name\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\d1\\\\client\\\\src\\\\app\\\\products\\\\page.js\",\n                                                            lineNumber: 197,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-600 text-sm mb-3 line-clamp-2\",\n                                                            children: product.description\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\d1\\\\client\\\\src\\\\app\\\\products\\\\page.js\",\n                                                            lineNumber: 198,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between mb-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center text-yellow-400\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FiStar, {\n                                                                                className: \"fill-current\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\d1\\\\client\\\\src\\\\app\\\\products\\\\page.js\",\n                                                                                lineNumber: 203,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-gray-600 text-sm mr-1\",\n                                                                                children: product.rating ? product.rating.toFixed(1) : \"0.0\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\d1\\\\client\\\\src\\\\app\\\\products\\\\page.js\",\n                                                                                lineNumber: 204,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"E:\\\\d1\\\\client\\\\src\\\\app\\\\products\\\\page.js\",\n                                                                        lineNumber: 202,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\d1\\\\client\\\\src\\\\app\\\\products\\\\page.js\",\n                                                                    lineNumber: 201,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-xl font-bold text-blue-600\",\n                                                                    children: [\n                                                                        product.price.toLocaleString(),\n                                                                        \" ر.س\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\d1\\\\client\\\\src\\\\app\\\\products\\\\page.js\",\n                                                                    lineNumber: 209,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\d1\\\\client\\\\src\\\\app\\\\products\\\\page.js\",\n                                                            lineNumber: 200,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Link, {\n                                                            href: \"/products/\".concat(product._id),\n                                                            className: \"btn btn-outline w-full\",\n                                                            children: \"عرض التفاصيل\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\d1\\\\client\\\\src\\\\app\\\\products\\\\page.js\",\n                                                            lineNumber: 214,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\d1\\\\client\\\\src\\\\app\\\\products\\\\page.js\",\n                                                    lineNumber: 196,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, product._id, true, {\n                                            fileName: \"E:\\\\d1\\\\client\\\\src\\\\app\\\\products\\\\page.js\",\n                                            lineNumber: 175,\n                                            columnNumber: 19\n                                        }, this);\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\d1\\\\client\\\\src\\\\app\\\\products\\\\page.js\",\n                                    lineNumber: 173,\n                                    columnNumber: 15\n                                }, this),\n                                totalPages > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-center mt-8\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex space-x-2 space-x-reverse\",\n                                        children: Array.from({\n                                            length: totalPages\n                                        }, (_, i)=>i + 1).map((page)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setCurrentPage(page),\n                                                className: \"px-4 py-2 rounded-lg \".concat(currentPage === page ? \"bg-blue-600 text-white\" : \"bg-gray-200 text-gray-700 hover:bg-gray-300\"),\n                                                children: page\n                                            }, page, false, {\n                                                fileName: \"E:\\\\d1\\\\client\\\\src\\\\app\\\\products\\\\page.js\",\n                                                lineNumber: 230,\n                                                columnNumber: 23\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\d1\\\\client\\\\src\\\\app\\\\products\\\\page.js\",\n                                        lineNumber: 228,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\d1\\\\client\\\\src\\\\app\\\\products\\\\page.js\",\n                                    lineNumber: 227,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\d1\\\\client\\\\src\\\\app\\\\products\\\\page.js\",\n                        lineNumber: 162,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\d1\\\\client\\\\src\\\\app\\\\products\\\\page.js\",\n                lineNumber: 98,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\d1\\\\client\\\\src\\\\app\\\\products\\\\page.js\",\n        lineNumber: 68,\n        columnNumber: 5\n    }, this);\n}\n_s(ProductsPage, \"SheD/RzmP6LlI3Mc91Aa1p293yM=\");\n_c = ProductsPage;\nvar _c;\n$RefreshReg$(_c, \"ProductsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/products/page.js\n"));

/***/ })

});