"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/products/page",{

/***/ "(app-pages-browser)/./src/app/products/page.js":
/*!**********************************!*\
  !*** ./src/app/products/page.js ***!
  \**********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ ProductsPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/../node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/../node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\nfunction ProductsPage() {\n    _s();\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [selectedCategory, setSelectedCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // Sample data\n    const products = [\n        {\n            _id: \"1\",\n            name: \"كرسي مكتب مريح\",\n            description: \"كرسي مكتب عالي الجودة مع دعم للظهر\",\n            price: 850,\n            category: \"مكتب\",\n            stock: 15,\n            rating: 4.5\n        },\n        {\n            _id: \"2\",\n            name: \"طاولة طعام خشبية\",\n            description: \"طاولة طعام من الخشب الطبيعي تتسع لـ 6 أشخاص\",\n            price: 1200,\n            category: \"طعام\",\n            stock: 8,\n            rating: 4.8\n        },\n        {\n            _id: \"3\",\n            name: \"سرير مزدوج\",\n            description: \"سرير مزدوج مريح من الخشب الطبيعي\",\n            price: 2500,\n            category: \"نوم\",\n            stock: 5,\n            rating: 4.7\n        },\n        {\n            _id: \"4\",\n            name: \"خزانة ملابس\",\n            description: \"خزانة ملابس واسعة بثلاث أبواب\",\n            price: 1800,\n            category: \"نوم\",\n            stock: 3,\n            rating: 4.6\n        }\n    ];\n    const categories = [\n        {\n            _id: \"1\",\n            name: \"غرفة المعيشة\"\n        },\n        {\n            _id: \"2\",\n            name: \"غرفة النوم\"\n        },\n        {\n            _id: \"3\",\n            name: \"المكتب\"\n        },\n        {\n            _id: \"4\",\n            name: \"غرفة الطعام\"\n        }\n    ];\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        fetchCategories();\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        fetchProducts();\n    }, [\n        searchTerm,\n        selectedCategory,\n        priceRange,\n        currentPage\n    ]);\n    const fetchCategories = async ()=>{\n        try {\n            const response = await api.get(\"/categories\");\n            setCategories(response.data);\n        } catch (error) {\n            console.error(\"Error fetching categories:\", error);\n        }\n    };\n    const fetchProducts = async ()=>{\n        setLoading(true);\n        try {\n            const params = new URLSearchParams({\n                page: currentPage,\n                limit: 12\n            });\n            if (searchTerm) params.append(\"search\", searchTerm);\n            if (selectedCategory) params.append(\"category\", selectedCategory);\n            if (priceRange.min) params.append(\"minPrice\", priceRange.min);\n            if (priceRange.max) params.append(\"maxPrice\", priceRange.max);\n            const response = await api.get(\"/products?\".concat(params));\n            setProducts(response.data.products || []);\n            setTotalPages(response.data.totalPages || 1);\n        } catch (error) {\n            console.error(\"Error fetching products:\", error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleSearch = (e)=>{\n        e.preventDefault();\n        setCurrentPage(1);\n        fetchProducts();\n    };\n    const handleAddToCart = (product)=>{\n        addToCart(product, 1);\n    };\n    const clearFilters = ()=>{\n        setSearchTerm(\"\");\n        setSelectedCategory(\"\");\n        setPriceRange({\n            min: \"\",\n            max: \"\"\n        });\n        setCurrentPage(1);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"container mx-auto px-4 py-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-3xl font-bold mb-4\",\n                        children: \"جميع المنتجات\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\d1\\\\client\\\\src\\\\app\\\\products\\\\page.js\",\n                        lineNumber: 118,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                        onSubmit: handleSearch,\n                        className: \"flex gap-2 mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        placeholder: \"ابحث عن المنتجات...\",\n                                        value: searchTerm,\n                                        onChange: (e)=>setSearchTerm(e.target.value),\n                                        className: \"form-input pl-10\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\d1\\\\client\\\\src\\\\app\\\\products\\\\page.js\",\n                                        lineNumber: 123,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FiSearch, {\n                                        className: \"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\d1\\\\client\\\\src\\\\app\\\\products\\\\page.js\",\n                                        lineNumber: 130,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\d1\\\\client\\\\src\\\\app\\\\products\\\\page.js\",\n                                lineNumber: 122,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"submit\",\n                                className: \"btn btn-primary\",\n                                children: \"بحث\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\d1\\\\client\\\\src\\\\app\\\\products\\\\page.js\",\n                                lineNumber: 132,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"button\",\n                                onClick: ()=>setShowFilters(!showFilters),\n                                className: \"btn btn-secondary md:hidden\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FiFilter, {}, void 0, false, {\n                                    fileName: \"E:\\\\d1\\\\client\\\\src\\\\app\\\\products\\\\page.js\",\n                                    lineNumber: 140,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\d1\\\\client\\\\src\\\\app\\\\products\\\\page.js\",\n                                lineNumber: 135,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\d1\\\\client\\\\src\\\\app\\\\products\\\\page.js\",\n                        lineNumber: 121,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\d1\\\\client\\\\src\\\\app\\\\products\\\\page.js\",\n                lineNumber: 117,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col md:flex-row gap-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"md:w-64 \".concat(showFilters ? \"block\" : \"hidden md:block\"),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white p-6 rounded-lg shadow-md\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between items-center mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold\",\n                                            children: \"الفلاتر\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\d1\\\\client\\\\src\\\\app\\\\products\\\\page.js\",\n                                            lineNumber: 150,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: clearFilters,\n                                            className: \"text-blue-600 text-sm hover:underline\",\n                                            children: \"مسح الكل\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\d1\\\\client\\\\src\\\\app\\\\products\\\\page.js\",\n                                            lineNumber: 151,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\d1\\\\client\\\\src\\\\app\\\\products\\\\page.js\",\n                                    lineNumber: 149,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"form-label\",\n                                            children: \"الفئة\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\d1\\\\client\\\\src\\\\app\\\\products\\\\page.js\",\n                                            lineNumber: 161,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                            value: selectedCategory,\n                                            onChange: (e)=>{\n                                                setSelectedCategory(e.target.value);\n                                                setCurrentPage(1);\n                                            },\n                                            className: \"form-select\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"\",\n                                                    children: \"جميع الفئات\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\d1\\\\client\\\\src\\\\app\\\\products\\\\page.js\",\n                                                    lineNumber: 170,\n                                                    columnNumber: 17\n                                                }, this),\n                                                categories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: category._id,\n                                                        children: category.name\n                                                    }, category._id, false, {\n                                                        fileName: \"E:\\\\d1\\\\client\\\\src\\\\app\\\\products\\\\page.js\",\n                                                        lineNumber: 172,\n                                                        columnNumber: 19\n                                                    }, this))\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\d1\\\\client\\\\src\\\\app\\\\products\\\\page.js\",\n                                            lineNumber: 162,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\d1\\\\client\\\\src\\\\app\\\\products\\\\page.js\",\n                                    lineNumber: 160,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"form-label\",\n                                            children: \"نطاق السعر\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\d1\\\\client\\\\src\\\\app\\\\products\\\\page.js\",\n                                            lineNumber: 181,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"number\",\n                                                    placeholder: \"من\",\n                                                    value: priceRange.min,\n                                                    onChange: (e)=>{\n                                                        setPriceRange((prev)=>({\n                                                                ...prev,\n                                                                min: e.target.value\n                                                            }));\n                                                        setCurrentPage(1);\n                                                    },\n                                                    className: \"form-input\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\d1\\\\client\\\\src\\\\app\\\\products\\\\page.js\",\n                                                    lineNumber: 183,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"number\",\n                                                    placeholder: \"إلى\",\n                                                    value: priceRange.max,\n                                                    onChange: (e)=>{\n                                                        setPriceRange((prev)=>({\n                                                                ...prev,\n                                                                max: e.target.value\n                                                            }));\n                                                        setCurrentPage(1);\n                                                    },\n                                                    className: \"form-input\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\d1\\\\client\\\\src\\\\app\\\\products\\\\page.js\",\n                                                    lineNumber: 193,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\d1\\\\client\\\\src\\\\app\\\\products\\\\page.js\",\n                                            lineNumber: 182,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\d1\\\\client\\\\src\\\\app\\\\products\\\\page.js\",\n                                    lineNumber: 180,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\d1\\\\client\\\\src\\\\app\\\\products\\\\page.js\",\n                            lineNumber: 148,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\d1\\\\client\\\\src\\\\app\\\\products\\\\page.js\",\n                        lineNumber: 147,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1\",\n                        children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-center items-center h-64\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"spinner border-blue-600\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\d1\\\\client\\\\src\\\\app\\\\products\\\\page.js\",\n                                lineNumber: 212,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\d1\\\\client\\\\src\\\\app\\\\products\\\\page.js\",\n                            lineNumber: 211,\n                            columnNumber: 13\n                        }, this) : products.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center py-12\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-500 text-lg\",\n                                children: \"لا توجد منتجات متاحة\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\d1\\\\client\\\\src\\\\app\\\\products\\\\page.js\",\n                                lineNumber: 216,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\d1\\\\client\\\\src\\\\app\\\\products\\\\page.js\",\n                            lineNumber: 215,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"product-grid\",\n                                    children: products.map((product)=>{\n                                        var _product_images;\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"card group\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative overflow-hidden\",\n                                                    children: [\n                                                        ((_product_images = product.images) === null || _product_images === void 0 ? void 0 : _product_images[0]) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Image, {\n                                                            src: product.images[0],\n                                                            alt: product.name,\n                                                            width: 300,\n                                                            height: 250,\n                                                            className: \"w-full h-64 object-cover group-hover:scale-105 transition-transform\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\d1\\\\client\\\\src\\\\app\\\\products\\\\page.js\",\n                                                            lineNumber: 225,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all flex items-center justify-center\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>handleAddToCart(product),\n                                                                className: \"bg-blue-600 text-white p-3 rounded-full opacity-0 group-hover:opacity-100 transform translate-y-4 group-hover:translate-y-0 transition-all\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FiShoppingCart, {\n                                                                    size: 20\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\d1\\\\client\\\\src\\\\app\\\\products\\\\page.js\",\n                                                                    lineNumber: 238,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\d1\\\\client\\\\src\\\\app\\\\products\\\\page.js\",\n                                                                lineNumber: 234,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\d1\\\\client\\\\src\\\\app\\\\products\\\\page.js\",\n                                                            lineNumber: 233,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\d1\\\\client\\\\src\\\\app\\\\products\\\\page.js\",\n                                                    lineNumber: 223,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"card-body\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"font-semibold text-lg mb-2 line-clamp-2\",\n                                                            children: product.name\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\d1\\\\client\\\\src\\\\app\\\\products\\\\page.js\",\n                                                            lineNumber: 244,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-600 text-sm mb-3 line-clamp-2\",\n                                                            children: product.description\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\d1\\\\client\\\\src\\\\app\\\\products\\\\page.js\",\n                                                            lineNumber: 245,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between mb-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center text-yellow-400\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FiStar, {\n                                                                                className: \"fill-current\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\d1\\\\client\\\\src\\\\app\\\\products\\\\page.js\",\n                                                                                lineNumber: 250,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-gray-600 text-sm mr-1\",\n                                                                                children: product.rating ? product.rating.toFixed(1) : \"0.0\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\d1\\\\client\\\\src\\\\app\\\\products\\\\page.js\",\n                                                                                lineNumber: 251,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"E:\\\\d1\\\\client\\\\src\\\\app\\\\products\\\\page.js\",\n                                                                        lineNumber: 249,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\d1\\\\client\\\\src\\\\app\\\\products\\\\page.js\",\n                                                                    lineNumber: 248,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-xl font-bold text-blue-600\",\n                                                                    children: [\n                                                                        product.price.toLocaleString(),\n                                                                        \" ر.س\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\d1\\\\client\\\\src\\\\app\\\\products\\\\page.js\",\n                                                                    lineNumber: 256,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\d1\\\\client\\\\src\\\\app\\\\products\\\\page.js\",\n                                                            lineNumber: 247,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Link, {\n                                                            href: \"/products/\".concat(product._id),\n                                                            className: \"btn btn-outline w-full\",\n                                                            children: \"عرض التفاصيل\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\d1\\\\client\\\\src\\\\app\\\\products\\\\page.js\",\n                                                            lineNumber: 261,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\d1\\\\client\\\\src\\\\app\\\\products\\\\page.js\",\n                                                    lineNumber: 243,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, product._id, true, {\n                                            fileName: \"E:\\\\d1\\\\client\\\\src\\\\app\\\\products\\\\page.js\",\n                                            lineNumber: 222,\n                                            columnNumber: 19\n                                        }, this);\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\d1\\\\client\\\\src\\\\app\\\\products\\\\page.js\",\n                                    lineNumber: 220,\n                                    columnNumber: 15\n                                }, this),\n                                totalPages > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-center mt-8\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex space-x-2 space-x-reverse\",\n                                        children: Array.from({\n                                            length: totalPages\n                                        }, (_, i)=>i + 1).map((page)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setCurrentPage(page),\n                                                className: \"px-4 py-2 rounded-lg \".concat(currentPage === page ? \"bg-blue-600 text-white\" : \"bg-gray-200 text-gray-700 hover:bg-gray-300\"),\n                                                children: page\n                                            }, page, false, {\n                                                fileName: \"E:\\\\d1\\\\client\\\\src\\\\app\\\\products\\\\page.js\",\n                                                lineNumber: 277,\n                                                columnNumber: 23\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\d1\\\\client\\\\src\\\\app\\\\products\\\\page.js\",\n                                        lineNumber: 275,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\d1\\\\client\\\\src\\\\app\\\\products\\\\page.js\",\n                                    lineNumber: 274,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\d1\\\\client\\\\src\\\\app\\\\products\\\\page.js\",\n                        lineNumber: 209,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\d1\\\\client\\\\src\\\\app\\\\products\\\\page.js\",\n                lineNumber: 145,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\d1\\\\client\\\\src\\\\app\\\\products\\\\page.js\",\n        lineNumber: 115,\n        columnNumber: 5\n    }, this);\n}\n_s(ProductsPage, \"GydRDroUDFyxrUkvmrYLrVifjz4=\");\n_c = ProductsPage;\nvar _c;\n$RefreshReg$(_c, \"ProductsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/products/page.js\n"));

/***/ })

});