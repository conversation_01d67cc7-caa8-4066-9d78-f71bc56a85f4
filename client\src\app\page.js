export default function Home() {
  // Sample data for testing
  const featuredProducts = [
    {
      _id: '1',
      name: 'كرسي مكتب مريح',
      description: 'كرسي مكتب عالي الجودة مع دعم للظهر',
      price: 850,
      category: 'مكتب',
      stock: 15,
      rating: 4.5
    },
    {
      _id: '2',
      name: 'طاولة طعام خشبية',
      description: 'طاولة طعام من الخشب الطبيعي تتسع لـ 6 أشخاص',
      price: 1200,
      category: 'طعام',
      stock: 8,
      rating: 4.8
    }
  ];

  const categories = [
    { _id: '1', name: 'غرفة المعيشة', description: 'أثاث غرفة المعيشة' },
    { _id: '2', name: 'غرفة النوم', description: 'أثاث غرفة النوم' },
    { _id: '3', name: 'المكتب', description: 'أثاث المكتب' },
    { _id: '4', name: 'غرفة الطعام', description: 'أثاث غرفة الطعام' }
  ];

  return (
    <main>
      {/* Hero Section */}
      <section className="relative bg-gradient-to-br from-blue-600 via-blue-700 to-purple-800 text-white py-24 overflow-hidden">
        <div className="absolute inset-0 bg-black opacity-20"></div>
        <div className="absolute inset-0">
          <div className="absolute top-10 left-10 w-20 h-20 bg-white opacity-10 rounded-full"></div>
          <div className="absolute bottom-10 right-10 w-32 h-32 bg-white opacity-5 rounded-full"></div>
          <div className="absolute top-1/2 left-1/2 w-16 h-16 bg-white opacity-10 rounded-full transform -translate-x-1/2 -translate-y-1/2"></div>
        </div>
        <div className="relative max-w-6xl mx-auto px-4 text-center">
          <div className="mb-8">
            <span className="inline-block text-6xl mb-4">🏠</span>
          </div>
          <h1 className="text-5xl md:text-7xl font-bold mb-6 leading-tight">
            متجر الأثاث
            <span className="block text-3xl md:text-4xl font-normal mt-2 text-blue-200">
              الأناقة تلتقي بالراحة
            </span>
          </h1>
          <p className="text-xl md:text-2xl mb-10 max-w-3xl mx-auto leading-relaxed text-blue-100">
            اكتشف مجموعتنا الواسعة من الأثاث عالي الجودة المصمم خصيصاً لجعل منزلك أكثر جمالاً وراحة
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <a
              href="/products"
              className="inline-flex items-center justify-center bg-white text-blue-600 px-8 py-4 rounded-xl font-bold text-lg hover:bg-blue-50 transition-all duration-300 transform hover:scale-105 shadow-lg"
            >
              <span className="ml-2">🛒</span>
              تسوق الآن
            </a>
            <a
              href="/about"
              className="inline-flex items-center justify-center border-2 border-white text-white px-8 py-4 rounded-xl font-bold text-lg hover:bg-white hover:text-blue-600 transition-all duration-300"
            >
              <span className="ml-2">ℹ️</span>
              تعرف علينا
            </a>
          </div>
        </div>
      </section>

      {/* Categories Section */}
      <section className="py-20 bg-gradient-to-b from-gray-50 to-white">
        <div className="max-w-6xl mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-gray-800 mb-4">تسوق حسب الفئة</h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              اختر من مجموعتنا المتنوعة من الأثاث المصمم خصيصاً لكل غرفة في منزلك
            </p>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {categories.map((category, index) => {
              const icons = ['🛋️', '🛏️', '🪑', '🍽️'];
              const colors = ['bg-blue-500', 'bg-green-500', 'bg-purple-500', 'bg-orange-500'];
              return (
                <div
                  key={category._id}
                  className="group bg-white rounded-2xl shadow-lg overflow-hidden hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2"
                >
                  <div className="p-8 text-center">
                    <div className={`w-20 h-20 ${colors[index]} rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300`}>
                      <span className="text-3xl">{icons[index]}</span>
                    </div>
                    <h3 className="text-xl font-bold text-gray-800 mb-3">{category.name}</h3>
                    <p className="text-gray-600 mb-6">{category.description}</p>
                    <button className="bg-gray-100 text-gray-800 px-6 py-2 rounded-full hover:bg-gray-200 transition-colors font-medium">
                      تصفح المنتجات
                    </button>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      </section>

      {/* Featured Products Section */}
      <section className="py-20 bg-white">
        <div className="max-w-6xl mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-gray-800 mb-4">المنتجات المميزة</h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto mb-8">
              اكتشف أحدث وأفضل منتجاتنا المختارة بعناية لتناسب ذوقك المميز
            </p>
            <a
              href="/products"
              className="inline-flex items-center text-blue-600 hover:text-blue-700 font-semibold text-lg border-b-2 border-blue-600 hover:border-blue-700 transition-colors"
            >
              عرض جميع المنتجات
              <span className="mr-2 text-xl">→</span>
            </a>
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-2 gap-8">
            {featuredProducts.map((product, index) => {
              const productIcons = ['🪑', '🍽️'];
              const gradients = ['from-blue-400 to-blue-600', 'from-green-400 to-green-600'];
              return (
                <div key={product._id} className="group bg-white rounded-2xl shadow-xl overflow-hidden hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-1">
                  <div className={`h-64 bg-gradient-to-br ${gradients[index]} flex items-center justify-center relative overflow-hidden`}>
                    <div className="absolute inset-0 bg-black opacity-10"></div>
                    <span className="text-8xl relative z-10">{productIcons[index]}</span>
                    <div className="absolute top-4 right-4 bg-white bg-opacity-20 backdrop-blur-sm rounded-full px-3 py-1">
                      <span className="text-white text-sm font-medium">جديد</span>
                    </div>
                  </div>

                  <div className="p-6">
                    <div className="flex items-center justify-between mb-3">
                      <span className="bg-blue-100 text-blue-800 text-xs font-medium px-2 py-1 rounded-full">
                        {product.category}
                      </span>
                      <div className="flex items-center">
                        <span className="text-yellow-400 text-lg">⭐</span>
                        <span className="text-gray-600 text-sm mr-1 font-medium">
                          {product.rating ? product.rating.toFixed(1) : '0.0'}
                        </span>
                      </div>
                    </div>

                    <h3 className="font-bold text-xl text-gray-800 mb-2 group-hover:text-blue-600 transition-colors">
                      {product.name}
                    </h3>
                    <p className="text-gray-600 mb-4 leading-relaxed">
                      {product.description}
                    </p>

                    <div className="flex items-center justify-between">
                      <div className="text-2xl font-bold text-blue-600">
                        {product.price.toLocaleString()} <span className="text-lg">ر.س</span>
                      </div>
                      <button className="bg-blue-600 text-white px-6 py-3 rounded-xl hover:bg-blue-700 transition-all duration-300 transform hover:scale-105 font-medium">
                        إضافة للسلة
                      </button>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 bg-gradient-to-br from-blue-50 via-white to-purple-50">
        <div className="max-w-6xl mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-gray-800 mb-4">لماذا تختارنا؟</h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              نحن نقدم أكثر من مجرد أثاث، نقدم تجربة متكاملة تضمن راحتك ورضاك التام
            </p>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="group text-center p-8 bg-white rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2">
              <div className="bg-gradient-to-br from-blue-400 to-blue-600 w-20 h-20 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300">
                <span className="text-3xl">🚚</span>
              </div>
              <h3 className="text-2xl font-bold text-gray-800 mb-4">شحن مجاني</h3>
              <p className="text-gray-600 leading-relaxed">
                شحن مجاني لجميع الطلبات أكثر من 500 ريال مع ضمان وصول آمن وسريع
              </p>
            </div>

            <div className="group text-center p-8 bg-white rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2">
              <div className="bg-gradient-to-br from-green-400 to-green-600 w-20 h-20 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300">
                <span className="text-3xl">🛡️</span>
              </div>
              <h3 className="text-2xl font-bold text-gray-800 mb-4">ضمان الجودة</h3>
              <p className="text-gray-600 leading-relaxed">
                ضمان شامل على جميع منتجاتنا لمدة سنتين مع خدمة ما بعد البيع المتميزة
              </p>
            </div>

            <div className="group text-center p-8 bg-white rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2">
              <div className="bg-gradient-to-br from-purple-400 to-purple-600 w-20 h-20 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300">
                <span className="text-3xl">🏠</span>
              </div>
              <h3 className="text-2xl font-bold text-gray-800 mb-4">تركيب مجاني</h3>
              <p className="text-gray-600 leading-relaxed">
                خدمة تركيب مجانية في منزلك من قبل فريق متخصص ومدرب على أعلى مستوى
              </p>
            </div>
          </div>
        </div>
      </section>
    </main>
  );
}