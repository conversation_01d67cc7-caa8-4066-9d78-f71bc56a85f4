export default function Home() {
  // Sample data for testing
  const featuredProducts = [
    {
      _id: '1',
      name: 'كرسي مكتب مريح',
      description: 'كرسي مكتب عالي الجودة مع دعم للظهر',
      price: 850,
      category: 'مكتب',
      stock: 15,
      rating: 4.5
    },
    {
      _id: '2',
      name: 'طاولة طعام خشبية',
      description: 'طاولة طعام من الخشب الطبيعي تتسع لـ 6 أشخاص',
      price: 1200,
      category: 'طعام',
      stock: 8,
      rating: 4.8
    }
  ];

  const categories = [
    { _id: '1', name: 'غرفة المعيشة', description: 'أثاث غرفة المعيشة' },
    { _id: '2', name: 'غرفة النوم', description: 'أثاث غرفة النوم' },
    { _id: '3', name: 'المكتب', description: 'أثاث المكتب' },
    { _id: '4', name: 'غرفة الطعام', description: 'أثاث غرفة الطعام' }
  ];

  return (
    <main>
      {/* Hero Section */}
      <section className="relative min-h-screen bg-gradient-to-br from-indigo-900 via-purple-900 to-pink-800 text-white overflow-hidden">
        {/* Animated Background Elements */}
        <div className="absolute inset-0">
          <div className="absolute top-20 left-20 w-32 h-32 bg-gradient-to-r from-yellow-400 to-orange-500 opacity-20 rounded-full animate-pulse"></div>
          <div className="absolute bottom-20 right-20 w-48 h-48 bg-gradient-to-r from-green-400 to-blue-500 opacity-15 rounded-full animate-bounce" style={{animationDuration: '3s'}}></div>
          <div className="absolute top-1/2 left-1/4 w-24 h-24 bg-gradient-to-r from-pink-400 to-red-500 opacity-25 rounded-full animate-ping" style={{animationDuration: '2s'}}></div>
          <div className="absolute bottom-1/3 left-1/2 w-16 h-16 bg-gradient-to-r from-purple-400 to-indigo-500 opacity-30 rounded-full animate-pulse" style={{animationDuration: '1.5s'}}></div>
        </div>

        {/* Floating Particles */}
        <div className="absolute inset-0">
          <div className="absolute top-1/4 right-1/3 w-2 h-2 bg-white opacity-60 rounded-full animate-float"></div>
          <div className="absolute top-3/4 left-1/4 w-3 h-3 bg-yellow-300 opacity-40 rounded-full animate-float-delayed"></div>
          <div className="absolute top-1/2 right-1/4 w-1 h-1 bg-pink-300 opacity-80 rounded-full animate-float"></div>
        </div>

        <div className="relative flex items-center justify-center min-h-screen">
          <div className="max-w-6xl mx-auto px-4 text-center">
            {/* Logo Animation */}
            <div className="mb-12 animate-bounce-slow">
              <div className="inline-block p-6 bg-gradient-to-r from-yellow-400 via-orange-500 to-red-500 rounded-full shadow-2xl transform hover:scale-110 transition-transform duration-300">
                <span className="text-8xl">🏠</span>
              </div>
            </div>

            {/* Main Title */}
            <h1 className="text-6xl md:text-8xl font-black mb-8 leading-tight">
              <span className="bg-gradient-to-r from-yellow-400 via-pink-500 to-purple-600 bg-clip-text text-transparent animate-gradient">
                متجر الأثاث
              </span>
              <span className="block text-3xl md:text-5xl font-light mt-4 text-gray-200 animate-fade-in-up">
                ✨ حيث تلتقي الأناقة بالراحة ✨
              </span>
            </h1>

            {/* Description */}
            <p className="text-xl md:text-3xl mb-12 max-w-4xl mx-auto leading-relaxed text-gray-300 animate-fade-in-up" style={{animationDelay: '0.3s'}}>
              🌟 اكتشف عالماً من الأثاث الفاخر والتصاميم العصرية التي تحول منزلك إلى قصر من الجمال والراحة 🌟
            </p>

            {/* CTA Buttons */}
            <div className="flex flex-col sm:flex-row gap-6 justify-center animate-fade-in-up" style={{animationDelay: '0.6s'}}>
              <a
                href="/products"
                className="group relative inline-flex items-center justify-center bg-gradient-to-r from-yellow-400 via-orange-500 to-red-500 text-white px-10 py-5 rounded-2xl font-bold text-xl hover:from-yellow-500 hover:via-orange-600 hover:to-red-600 transition-all duration-300 transform hover:scale-105 hover:-translate-y-2 shadow-2xl hover:shadow-yellow-500/25"
              >
                <span className="absolute inset-0 bg-gradient-to-r from-yellow-400 via-orange-500 to-red-500 rounded-2xl blur opacity-75 group-hover:opacity-100 transition duration-300"></span>
                <span className="relative flex items-center">
                  <span className="ml-3 text-2xl animate-bounce">🛒</span>
                  تسوق الآن واستمتع
                </span>
              </a>

              <a
                href="/about"
                className="group relative inline-flex items-center justify-center bg-transparent border-3 border-gradient-to-r from-pink-400 to-purple-500 text-white px-10 py-5 rounded-2xl font-bold text-xl hover:bg-gradient-to-r hover:from-pink-500 hover:to-purple-600 transition-all duration-300 transform hover:scale-105 hover:-translate-y-2 shadow-2xl"
                style={{borderImage: 'linear-gradient(45deg, #f472b6, #a855f7) 1'}}
              >
                <span className="flex items-center">
                  <span className="ml-3 text-2xl">💎</span>
                  اكتشف قصتنا
                </span>
              </a>
            </div>

            {/* Scroll Indicator */}
            <div className="absolute bottom-10 left-1/2 transform -translate-x-1/2 animate-bounce">
              <div className="w-6 h-10 border-2 border-white rounded-full flex justify-center">
                <div className="w-1 h-3 bg-white rounded-full mt-2 animate-pulse"></div>
              </div>
              <p className="text-sm mt-2 text-gray-300">اسحب للأسفل</p>
            </div>
          </div>
        </div>
      </section>

      {/* Categories Section */}
      <section className="py-24 bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 relative overflow-hidden">
        {/* Background Decorations */}
        <div className="absolute inset-0">
          <div className="absolute top-10 right-10 w-64 h-64 bg-gradient-to-r from-blue-200 to-purple-200 opacity-30 rounded-full blur-3xl"></div>
          <div className="absolute bottom-10 left-10 w-48 h-48 bg-gradient-to-r from-pink-200 to-yellow-200 opacity-40 rounded-full blur-2xl"></div>
        </div>

        <div className="relative max-w-7xl mx-auto px-4">
          <div className="text-center mb-20">
            <div className="inline-block mb-6">
              <span className="bg-gradient-to-r from-blue-600 to-purple-600 text-white px-6 py-2 rounded-full text-sm font-semibold">
                🏆 أفضل التصنيفات
              </span>
            </div>
            <h2 className="text-5xl md:text-6xl font-black text-gray-800 mb-6">
              <span className="bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600 bg-clip-text text-transparent">
                تسوق حسب الفئة
              </span>
            </h2>
            <p className="text-2xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
              ✨ اختر من مجموعتنا المتنوعة من الأثاث الفاخر المصمم خصيصاً لكل غرفة في قصرك ✨
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {categories.map((category, index) => {
              const icons = ['🛋️', '🛏️', '🪑', '🍽️'];
              const gradients = [
                'from-blue-500 via-blue-600 to-indigo-700',
                'from-green-500 via-emerald-600 to-teal-700',
                'from-purple-500 via-violet-600 to-purple-700',
                'from-orange-500 via-red-500 to-pink-600'
              ];
              const glowColors = [
                'hover:shadow-blue-500/50',
                'hover:shadow-green-500/50',
                'hover:shadow-purple-500/50',
                'hover:shadow-orange-500/50'
              ];

              return (
                <div
                  key={category._id}
                  className={`group relative bg-white rounded-3xl shadow-xl overflow-hidden hover:shadow-2xl ${glowColors[index]} transition-all duration-500 transform hover:-translate-y-4 hover:scale-105`}
                >
                  {/* Gradient Border */}
                  <div className={`absolute inset-0 bg-gradient-to-r ${gradients[index]} opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-3xl`}></div>
                  <div className="relative bg-white m-1 rounded-3xl p-8 text-center">

                    {/* Icon Container */}
                    <div className="relative mb-8">
                      <div className={`w-24 h-24 bg-gradient-to-r ${gradients[index]} rounded-3xl flex items-center justify-center mx-auto group-hover:scale-110 group-hover:rotate-6 transition-all duration-300 shadow-lg`}>
                        <span className="text-4xl filter drop-shadow-lg">{icons[index]}</span>
                      </div>
                      {/* Floating particles around icon */}
                      <div className="absolute -top-2 -right-2 w-3 h-3 bg-yellow-400 rounded-full opacity-0 group-hover:opacity-100 group-hover:animate-ping transition-all duration-300"></div>
                      <div className="absolute -bottom-2 -left-2 w-2 h-2 bg-pink-400 rounded-full opacity-0 group-hover:opacity-100 group-hover:animate-pulse transition-all duration-300"></div>
                    </div>

                    {/* Content */}
                    <h3 className="text-2xl font-black text-gray-800 mb-4 group-hover:text-transparent group-hover:bg-gradient-to-r group-hover:from-blue-600 group-hover:to-purple-600 group-hover:bg-clip-text transition-all duration-300">
                      {category.name}
                    </h3>
                    <p className="text-gray-600 mb-8 leading-relaxed group-hover:text-gray-700 transition-colors duration-300">
                      {category.description}
                    </p>

                    {/* CTA Button */}
                    <button className={`relative bg-gradient-to-r ${gradients[index]} text-white px-8 py-3 rounded-2xl font-bold hover:shadow-lg transform hover:scale-105 transition-all duration-300 overflow-hidden group-hover:animate-pulse`}>
                      <span className="relative z-10">🚀 تصفح المنتجات</span>
                      <div className="absolute inset-0 bg-white opacity-0 group-hover:opacity-20 transition-opacity duration-300"></div>
                    </button>
                  </div>

                  {/* Decorative elements */}
                  <div className="absolute top-4 right-4 w-8 h-8 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-full opacity-0 group-hover:opacity-100 group-hover:animate-bounce transition-all duration-300"></div>
                </div>
              );
            })}
          </div>
        </div>
      </section>

      {/* Featured Products Section */}
      <section className="py-24 bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900 relative overflow-hidden">
        {/* Animated Background */}
        <div className="absolute inset-0">
          <div className="absolute top-20 left-20 w-96 h-96 bg-gradient-to-r from-blue-500 to-purple-500 opacity-20 rounded-full blur-3xl animate-pulse"></div>
          <div className="absolute bottom-20 right-20 w-80 h-80 bg-gradient-to-r from-pink-500 to-yellow-500 opacity-15 rounded-full blur-3xl animate-bounce" style={{animationDuration: '4s'}}></div>
          <div className="absolute top-1/2 left-1/2 w-64 h-64 bg-gradient-to-r from-green-400 to-blue-500 opacity-10 rounded-full blur-2xl animate-ping" style={{animationDuration: '3s'}}></div>
        </div>

        <div className="relative max-w-7xl mx-auto px-4">
          <div className="text-center mb-20">
            <div className="inline-block mb-8">
              <span className="bg-gradient-to-r from-yellow-400 via-orange-500 to-red-500 text-white px-8 py-3 rounded-full text-lg font-bold shadow-lg">
                ⭐ منتجات مميزة ⭐
              </span>
            </div>
            <h2 className="text-5xl md:text-7xl font-black text-white mb-8">
              <span className="bg-gradient-to-r from-yellow-400 via-pink-500 to-purple-400 bg-clip-text text-transparent animate-gradient">
                أحدث إبداعاتنا
              </span>
            </h2>
            <p className="text-2xl text-gray-300 max-w-4xl mx-auto mb-12 leading-relaxed">
              🌟 اكتشف أحدث وأفضل منتجاتنا المختارة بعناية فائقة لتناسب ذوقك الرفيع وتحول منزلك إلى تحفة فنية 🌟
            </p>
            <a
              href="/products"
              className="group inline-flex items-center bg-gradient-to-r from-yellow-400 via-orange-500 to-red-500 text-white px-10 py-4 rounded-2xl font-bold text-xl hover:from-yellow-500 hover:via-orange-600 hover:to-red-600 transition-all duration-300 transform hover:scale-105 hover:-translate-y-1 shadow-2xl hover:shadow-yellow-500/25"
            >
              <span className="ml-3 text-2xl group-hover:animate-bounce">🚀</span>
              عرض جميع المنتجات الفاخرة
            </a>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
            {featuredProducts.map((product, index) => {
              const productIcons = ['🪑', '🍽️'];
              const gradients = [
                'from-cyan-400 via-blue-500 to-purple-600',
                'from-green-400 via-emerald-500 to-teal-600'
              ];
              const glowColors = [
                'hover:shadow-blue-500/50',
                'hover:shadow-green-500/50'
              ];

              return (
                <div key={product._id} className={`group relative bg-white rounded-3xl shadow-2xl overflow-hidden hover:shadow-3xl ${glowColors[index]} transition-all duration-500 transform hover:-translate-y-6 hover:scale-105`}>
                  {/* Product Image/Icon Section */}
                  <div className={`relative h-80 bg-gradient-to-br ${gradients[index]} flex items-center justify-center overflow-hidden`}>
                    {/* Animated Background Elements */}
                    <div className="absolute inset-0">
                      <div className="absolute top-10 right-10 w-20 h-20 bg-white opacity-20 rounded-full animate-float"></div>
                      <div className="absolute bottom-10 left-10 w-16 h-16 bg-yellow-300 opacity-30 rounded-full animate-float-delayed"></div>
                      <div className="absolute top-1/2 left-1/2 w-12 h-12 bg-pink-300 opacity-25 rounded-full animate-pulse"></div>
                    </div>

                    {/* Main Product Icon */}
                    <div className="relative z-10 group-hover:scale-110 group-hover:rotate-6 transition-all duration-500">
                      <span className="text-9xl filter drop-shadow-2xl">{productIcons[index]}</span>
                    </div>

                    {/* Badges */}
                    <div className="absolute top-6 right-6 bg-gradient-to-r from-yellow-400 to-orange-500 text-white rounded-2xl px-4 py-2 font-bold shadow-lg">
                      <span className="text-sm">🔥 الأكثر مبيعاً</span>
                    </div>
                    <div className="absolute top-6 left-6 bg-white bg-opacity-20 backdrop-blur-sm rounded-2xl px-4 py-2">
                      <span className="text-white text-sm font-bold">✨ جديد</span>
                    </div>

                    {/* Floating Action Button */}
                    <div className="absolute bottom-6 right-6 opacity-0 group-hover:opacity-100 transition-all duration-300 transform translate-y-4 group-hover:translate-y-0">
                      <button className="bg-white text-gray-800 p-4 rounded-full shadow-lg hover:shadow-xl transform hover:scale-110 transition-all duration-300">
                        <span className="text-2xl">❤️</span>
                      </button>
                    </div>
                  </div>

                  {/* Product Details */}
                  <div className="p-8">
                    <div className="flex items-center justify-between mb-4">
                      <span className={`bg-gradient-to-r ${gradients[index]} text-white text-sm font-bold px-4 py-2 rounded-full shadow-md`}>
                        {product.category}
                      </span>
                      <div className="flex items-center bg-yellow-50 rounded-full px-3 py-1">
                        <span className="text-yellow-500 text-xl">⭐</span>
                        <span className="text-gray-800 text-sm mr-2 font-bold">
                          {product.rating ? product.rating.toFixed(1) : '0.0'}
                        </span>
                      </div>
                    </div>

                    <h3 className="font-black text-3xl text-gray-800 mb-4 group-hover:text-transparent group-hover:bg-gradient-to-r group-hover:from-blue-600 group-hover:to-purple-600 group-hover:bg-clip-text transition-all duration-300">
                      {product.name}
                    </h3>
                    <p className="text-gray-600 mb-8 leading-relaxed text-lg">
                      {product.description}
                    </p>

                    <div className="flex items-center justify-between">
                      <div className="text-4xl font-black">
                        <span className="bg-gradient-to-r from-green-600 to-blue-600 bg-clip-text text-transparent">
                          {product.price.toLocaleString()}
                        </span>
                        <span className="text-2xl text-gray-600 mr-2">ر.س</span>
                      </div>
                      <button className={`group relative bg-gradient-to-r ${gradients[index]} text-white px-8 py-4 rounded-2xl font-bold text-lg hover:shadow-lg transform hover:scale-105 transition-all duration-300 overflow-hidden`}>
                        <span className="relative z-10 flex items-center">
                          <span className="ml-2 text-xl group-hover:animate-bounce">🛒</span>
                          إضافة للسلة
                        </span>
                        <div className="absolute inset-0 bg-white opacity-0 group-hover:opacity-20 transition-opacity duration-300"></div>
                      </button>
                    </div>
                  </div>

                  {/* Decorative Corner Elements */}
                  <div className="absolute top-0 left-0 w-0 h-0 border-t-[40px] border-r-[40px] border-t-yellow-400 border-r-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                  <div className="absolute bottom-0 right-0 w-0 h-0 border-b-[40px] border-l-[40px] border-b-pink-400 border-l-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                </div>
              );
            })}
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 bg-gradient-to-br from-blue-50 via-white to-purple-50">
        <div className="max-w-6xl mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-gray-800 mb-4">لماذا تختارنا؟</h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              نحن نقدم أكثر من مجرد أثاث، نقدم تجربة متكاملة تضمن راحتك ورضاك التام
            </p>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="group text-center p-8 bg-white rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2">
              <div className="bg-gradient-to-br from-blue-400 to-blue-600 w-20 h-20 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300">
                <span className="text-3xl">🚚</span>
              </div>
              <h3 className="text-2xl font-bold text-gray-800 mb-4">شحن مجاني</h3>
              <p className="text-gray-600 leading-relaxed">
                شحن مجاني لجميع الطلبات أكثر من 500 ريال مع ضمان وصول آمن وسريع
              </p>
            </div>

            <div className="group text-center p-8 bg-white rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2">
              <div className="bg-gradient-to-br from-green-400 to-green-600 w-20 h-20 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300">
                <span className="text-3xl">🛡️</span>
              </div>
              <h3 className="text-2xl font-bold text-gray-800 mb-4">ضمان الجودة</h3>
              <p className="text-gray-600 leading-relaxed">
                ضمان شامل على جميع منتجاتنا لمدة سنتين مع خدمة ما بعد البيع المتميزة
              </p>
            </div>

            <div className="group text-center p-8 bg-white rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2">
              <div className="bg-gradient-to-br from-purple-400 to-purple-600 w-20 h-20 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300">
                <span className="text-3xl">🏠</span>
              </div>
              <h3 className="text-2xl font-bold text-gray-800 mb-4">تركيب مجاني</h3>
              <p className="text-gray-600 leading-relaxed">
                خدمة تركيب مجانية في منزلك من قبل فريق متخصص ومدرب على أعلى مستوى
              </p>
            </div>
          </div>
        </div>
      </section>
    </main>
  );
}