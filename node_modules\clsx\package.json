{"name": "clsx", "version": "1.2.1", "repository": "lukeed/clsx", "description": "A tiny (228B) utility for constructing className strings conditionally.", "module": "dist/clsx.m.js", "unpkg": "dist/clsx.min.js", "main": "dist/clsx.js", "types": "clsx.d.ts", "license": "MIT", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://lukeed.com"}, "engines": {"node": ">=6"}, "scripts": {"build": "node bin", "test": "uvu -r esm test"}, "files": ["*.d.ts", "dist"], "keywords": ["classes", "classname", "classnames"], "devDependencies": {"esm": "3.2.25", "terser": "4.8.0", "uvu": "0.5.4"}}