import './globals.css';

export const metadata = {
  title: 'متجر الأثاث - أفضل الأثاث عالي الجودة',
  description: 'اكتشف مجموعتنا الواسعة من الأثاث عالي الجودة بأفضل الأسعار',
};

export default function RootLayout({ children }) {
  return (
    <html lang="ar" dir="rtl">
      <body>
        <div className="min-h-screen flex flex-col">
          {/* Enhanced Navbar */}
          <nav className="bg-white/95 backdrop-blur-md shadow-2xl border-b border-gray-100 sticky top-0 z-50">
            <div className="max-w-7xl mx-auto px-4">
              <div className="flex justify-between items-center h-24">
                {/* Logo */}
                <div className="flex items-center">
                  <div className="group cursor-pointer">
                    <div className="flex items-center space-x-3 space-x-reverse">
                      <div className="w-12 h-12 bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600 rounded-2xl flex items-center justify-center transform group-hover:scale-110 group-hover:rotate-6 transition-all duration-300 shadow-lg">
                        <span className="text-2xl">🏠</span>
                      </div>
                      <div>
                        <div className="text-2xl font-black text-transparent bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600 bg-clip-text">
                          متجر الأثاث
                        </div>
                        <div className="text-xs text-gray-500 font-medium">
                          ✨ الأناقة والجودة ✨
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Navigation Links */}
                <div className="hidden md:flex space-x-8 space-x-reverse">
                  <a href="/" className="group relative text-gray-700 hover:text-transparent hover:bg-gradient-to-r hover:from-blue-600 hover:to-purple-600 hover:bg-clip-text font-bold text-lg transition-all duration-300 py-2 px-4 rounded-xl hover:bg-gradient-to-r hover:from-blue-50 hover:to-purple-50">
                    🏠 الرئيسية
                    <span className="absolute bottom-0 left-0 w-0 h-1 bg-gradient-to-r from-blue-600 to-purple-600 group-hover:w-full transition-all duration-300 rounded-full"></span>
                  </a>
                  <a href="/products" className="group relative text-gray-700 hover:text-transparent hover:bg-gradient-to-r hover:from-green-600 hover:to-blue-600 hover:bg-clip-text font-bold text-lg transition-all duration-300 py-2 px-4 rounded-xl hover:bg-gradient-to-r hover:from-green-50 hover:to-blue-50">
                    🛍️ المنتجات
                    <span className="absolute bottom-0 left-0 w-0 h-1 bg-gradient-to-r from-green-600 to-blue-600 group-hover:w-full transition-all duration-300 rounded-full"></span>
                  </a>
                  <a href="/categories" className="group relative text-gray-700 hover:text-transparent hover:bg-gradient-to-r hover:from-purple-600 hover:to-pink-600 hover:bg-clip-text font-bold text-lg transition-all duration-300 py-2 px-4 rounded-xl hover:bg-gradient-to-r hover:from-purple-50 hover:to-pink-50">
                    📂 الأقسام
                    <span className="absolute bottom-0 left-0 w-0 h-1 bg-gradient-to-r from-purple-600 to-pink-600 group-hover:w-full transition-all duration-300 rounded-full"></span>
                  </a>
                  <a href="/about" className="group relative text-gray-700 hover:text-transparent hover:bg-gradient-to-r hover:from-orange-600 hover:to-red-600 hover:bg-clip-text font-bold text-lg transition-all duration-300 py-2 px-4 rounded-xl hover:bg-gradient-to-r hover:from-orange-50 hover:to-red-50">
                    ℹ️ من نحن
                    <span className="absolute bottom-0 left-0 w-0 h-1 bg-gradient-to-r from-orange-600 to-red-600 group-hover:w-full transition-all duration-300 rounded-full"></span>
                  </a>
                  <a href="/contact" className="group relative text-gray-700 hover:text-transparent hover:bg-gradient-to-r hover:from-teal-600 hover:to-cyan-600 hover:bg-clip-text font-bold text-lg transition-all duration-300 py-2 px-4 rounded-xl hover:bg-gradient-to-r hover:from-teal-50 hover:to-cyan-50">
                    📞 تواصل معنا
                    <span className="absolute bottom-0 left-0 w-0 h-1 bg-gradient-to-r from-teal-600 to-cyan-600 group-hover:w-full transition-all duration-300 rounded-full"></span>
                  </a>
                </div>

                {/* Action Buttons */}
                <div className="flex items-center space-x-4 space-x-reverse">
                  {/* Cart Button */}
                  <button className="group relative p-3 text-gray-700 hover:text-white hover:bg-gradient-to-r hover:from-blue-600 hover:to-purple-600 transition-all duration-300 rounded-2xl hover:shadow-lg transform hover:scale-105">
                    <span className="text-2xl group-hover:animate-bounce">🛒</span>
                    <span className="absolute -top-1 -right-1 bg-gradient-to-r from-red-500 to-pink-500 text-white text-xs rounded-full h-6 w-6 flex items-center justify-center font-bold shadow-lg animate-pulse">
                      0
                    </span>
                  </button>

                  {/* Search Button */}
                  <button className="group relative p-3 text-gray-700 hover:text-white hover:bg-gradient-to-r hover:from-green-600 hover:to-teal-600 transition-all duration-300 rounded-2xl hover:shadow-lg transform hover:scale-105">
                    <span className="text-2xl group-hover:animate-pulse">🔍</span>
                  </button>

                  {/* Login Button */}
                  <button className="group relative bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600 text-white px-8 py-3 rounded-2xl font-bold hover:from-blue-700 hover:via-purple-700 hover:to-pink-700 transition-all duration-300 transform hover:scale-105 hover:-translate-y-1 shadow-lg hover:shadow-xl overflow-hidden">
                    <span className="relative z-10 flex items-center">
                      <span className="ml-2 text-lg">👤</span>
                      تسجيل الدخول
                    </span>
                    <div className="absolute inset-0 bg-white opacity-0 group-hover:opacity-20 transition-opacity duration-300"></div>
                  </button>
                </div>
              </div>
            </div>
          </nav>

          <main className="flex-grow">
            {children}
          </main>

          {/* Enhanced Footer */}
          <footer className="bg-gradient-to-br from-gray-800 via-gray-900 to-black text-white">
            <div className="max-w-6xl mx-auto px-4 py-16">
              <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
                {/* Company Info */}
                <div className="md:col-span-2">
                  <div className="text-3xl font-bold mb-4 text-transparent bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text">
                    🏠 متجر الأثاث
                  </div>
                  <p className="text-gray-300 mb-6 leading-relaxed">
                    نحن نقدم أفضل الأثاث عالي الجودة بأسعار منافسة.
                    اكتشف مجموعتنا الواسعة من الأثاث المنزلي والمكتبي المصمم خصيصاً لراحتك.
                  </p>
                  <div className="flex space-x-4 space-x-reverse">
                    <a href="#" className="w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center hover:bg-blue-700 transition-colors">
                      <span>📘</span>
                    </a>
                    <a href="#" className="w-10 h-10 bg-blue-400 rounded-full flex items-center justify-center hover:bg-blue-500 transition-colors">
                      <span>🐦</span>
                    </a>
                    <a href="#" className="w-10 h-10 bg-pink-600 rounded-full flex items-center justify-center hover:bg-pink-700 transition-colors">
                      <span>📷</span>
                    </a>
                  </div>
                </div>

                {/* Quick Links */}
                <div>
                  <h3 className="text-lg font-semibold mb-4">روابط سريعة</h3>
                  <ul className="space-y-2">
                    <li><a href="/" className="text-gray-300 hover:text-white transition-colors">الرئيسية</a></li>
                    <li><a href="/products" className="text-gray-300 hover:text-white transition-colors">المنتجات</a></li>
                    <li><a href="/categories" className="text-gray-300 hover:text-white transition-colors">الأقسام</a></li>
                    <li><a href="/about" className="text-gray-300 hover:text-white transition-colors">من نحن</a></li>
                    <li><a href="/contact" className="text-gray-300 hover:text-white transition-colors">تواصل معنا</a></li>
                  </ul>
                </div>

                {/* Contact Info */}
                <div>
                  <h3 className="text-lg font-semibold mb-4">تواصل معنا</h3>
                  <div className="space-y-3">
                    <div className="flex items-center space-x-3 space-x-reverse">
                      <span className="text-blue-400">📞</span>
                      <span className="text-gray-300">+966 50 123 4567</span>
                    </div>
                    <div className="flex items-center space-x-3 space-x-reverse">
                      <span className="text-blue-400">📧</span>
                      <span className="text-gray-300"><EMAIL></span>
                    </div>
                    <div className="flex items-center space-x-3 space-x-reverse">
                      <span className="text-blue-400">📍</span>
                      <span className="text-gray-300">الرياض، المملكة العربية السعودية</span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Bottom Bar */}
              <div className="border-t border-gray-700 mt-12 pt-8 text-center">
                <p className="text-gray-400">
                  © 2024 متجر الأثاث. جميع الحقوق محفوظة. | صُمم بـ ❤️ في المملكة العربية السعودية
                </p>
              </div>
            </div>
          </footer>
        </div>
      </body>
    </html>
  );
}