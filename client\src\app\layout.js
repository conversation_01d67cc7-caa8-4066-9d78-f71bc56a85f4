import './globals.css';

export const metadata = {
  title: 'متجر الأثاث - أفضل الأثاث عالي الجودة',
  description: 'اكتشف مجموعتنا الواسعة من الأثاث عالي الجودة بأفضل الأسعار',
};

export default function RootLayout({ children }) {
  return (
    <html lang="ar" dir="rtl">
      <body>
        <div className="min-h-screen flex flex-col">
          {/* Enhanced Navbar */}
          <nav className="bg-white shadow-xl border-b border-gray-100 sticky top-0 z-50">
            <div className="max-w-6xl mx-auto px-4">
              <div className="flex justify-between items-center h-20">
                <div className="flex items-center">
                  <div className="text-3xl font-bold text-transparent bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text">
                    🏠 متجر الأثاث
                  </div>
                </div>
                <div className="hidden md:flex space-x-8 space-x-reverse">
                  <a href="/" className="text-gray-700 hover:text-blue-600 font-medium transition-colors duration-200 relative group">
                    الرئيسية
                    <span className="absolute bottom-0 left-0 w-0 h-0.5 bg-blue-600 group-hover:w-full transition-all duration-200"></span>
                  </a>
                  <a href="/products" className="text-gray-700 hover:text-blue-600 font-medium transition-colors duration-200 relative group">
                    المنتجات
                    <span className="absolute bottom-0 left-0 w-0 h-0.5 bg-blue-600 group-hover:w-full transition-all duration-200"></span>
                  </a>
                  <a href="/categories" className="text-gray-700 hover:text-blue-600 font-medium transition-colors duration-200 relative group">
                    الأقسام
                    <span className="absolute bottom-0 left-0 w-0 h-0.5 bg-blue-600 group-hover:w-full transition-all duration-200"></span>
                  </a>
                  <a href="/about" className="text-gray-700 hover:text-blue-600 font-medium transition-colors duration-200 relative group">
                    من نحن
                    <span className="absolute bottom-0 left-0 w-0 h-0.5 bg-blue-600 group-hover:w-full transition-all duration-200"></span>
                  </a>
                  <a href="/contact" className="text-gray-700 hover:text-blue-600 font-medium transition-colors duration-200 relative group">
                    تواصل معنا
                    <span className="absolute bottom-0 left-0 w-0 h-0.5 bg-blue-600 group-hover:w-full transition-all duration-200"></span>
                  </a>
                </div>
                <div className="flex items-center space-x-4 space-x-reverse">
                  <button className="relative p-2 text-gray-700 hover:text-blue-600 transition-colors">
                    <span className="text-2xl">🛒</span>
                    <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
                      0
                    </span>
                  </button>
                  <button className="bg-blue-600 text-white px-6 py-2 rounded-full hover:bg-blue-700 transition-colors font-medium">
                    تسجيل الدخول
                  </button>
                </div>
              </div>
            </div>
          </nav>

          <main className="flex-grow">
            {children}
          </main>

          {/* Enhanced Footer */}
          <footer className="bg-gradient-to-br from-gray-800 via-gray-900 to-black text-white">
            <div className="max-w-6xl mx-auto px-4 py-16">
              <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
                {/* Company Info */}
                <div className="md:col-span-2">
                  <div className="text-3xl font-bold mb-4 text-transparent bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text">
                    🏠 متجر الأثاث
                  </div>
                  <p className="text-gray-300 mb-6 leading-relaxed">
                    نحن نقدم أفضل الأثاث عالي الجودة بأسعار منافسة.
                    اكتشف مجموعتنا الواسعة من الأثاث المنزلي والمكتبي المصمم خصيصاً لراحتك.
                  </p>
                  <div className="flex space-x-4 space-x-reverse">
                    <a href="#" className="w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center hover:bg-blue-700 transition-colors">
                      <span>📘</span>
                    </a>
                    <a href="#" className="w-10 h-10 bg-blue-400 rounded-full flex items-center justify-center hover:bg-blue-500 transition-colors">
                      <span>🐦</span>
                    </a>
                    <a href="#" className="w-10 h-10 bg-pink-600 rounded-full flex items-center justify-center hover:bg-pink-700 transition-colors">
                      <span>📷</span>
                    </a>
                  </div>
                </div>

                {/* Quick Links */}
                <div>
                  <h3 className="text-lg font-semibold mb-4">روابط سريعة</h3>
                  <ul className="space-y-2">
                    <li><a href="/" className="text-gray-300 hover:text-white transition-colors">الرئيسية</a></li>
                    <li><a href="/products" className="text-gray-300 hover:text-white transition-colors">المنتجات</a></li>
                    <li><a href="/categories" className="text-gray-300 hover:text-white transition-colors">الأقسام</a></li>
                    <li><a href="/about" className="text-gray-300 hover:text-white transition-colors">من نحن</a></li>
                    <li><a href="/contact" className="text-gray-300 hover:text-white transition-colors">تواصل معنا</a></li>
                  </ul>
                </div>

                {/* Contact Info */}
                <div>
                  <h3 className="text-lg font-semibold mb-4">تواصل معنا</h3>
                  <div className="space-y-3">
                    <div className="flex items-center space-x-3 space-x-reverse">
                      <span className="text-blue-400">📞</span>
                      <span className="text-gray-300">+966 50 123 4567</span>
                    </div>
                    <div className="flex items-center space-x-3 space-x-reverse">
                      <span className="text-blue-400">📧</span>
                      <span className="text-gray-300"><EMAIL></span>
                    </div>
                    <div className="flex items-center space-x-3 space-x-reverse">
                      <span className="text-blue-400">📍</span>
                      <span className="text-gray-300">الرياض، المملكة العربية السعودية</span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Bottom Bar */}
              <div className="border-t border-gray-700 mt-12 pt-8 text-center">
                <p className="text-gray-400">
                  © 2024 متجر الأثاث. جميع الحقوق محفوظة. | صُمم بـ ❤️ في المملكة العربية السعودية
                </p>
              </div>
            </div>
          </footer>
        </div>
      </body>
    </html>
  );
}