/**
 * @license React
 * eslint-plugin-react-hooks.production.min.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
'use strict';function ha(a,d){if(a){if("string"===typeof a)return ma(a,d);var f=Object.prototype.toString.call(a).slice(8,-1);"Object"===f&&a.constructor&&(f=a.constructor.name);if("Map"===f||"Set"===f)return Array.from(a);if("Arguments"===f||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(f))return ma(a,d)}}function ma(a,d){if(null==d||d>a.length)d=a.length;for(var f=0,p=Array(d);f<d;f++)p[f]=a[f];return p}
function P(a,d){var f;if("undefined"===typeof Symbol||null==a[Symbol.iterator]){if(Array.isArray(a)||(f=ha(a))||d&&a&&"number"===typeof a.length){f&&(a=f);var p=0;d=function(){};return{s:d,n:function(){return p>=a.length?{done:!0}:{done:!1,value:a[p++]}},e:function(h){throw h;},f:d}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.");}var t=!0,H=!1,r;return{s:function(){f=a[Symbol.iterator]()},
n:function(){var h=f.next();t=h.done;return h},e:function(h){H=!0;r=h},f:function(){try{t||null==f.return||f.return()}finally{if(H)throw r;}}}}function na(a){if("Identifier"===a.type)return/^use[A-Z0-9]/.test(a.name);if("MemberExpression"===a.type&&!a.computed&&na(a.property)){a=a.object;var d=/^[A-Z].*/;return"Identifier"===a.type&&d.test(a.name)}return!1}function oa(a){return"Identifier"===a.type&&/^[A-Z]/.test(a.name)}
function pa(a,d){return a.name===d||"MemberExpression"===a.type&&"React"===a.object.name&&a.property.name===d}function ta(a){return!!(a.parent&&a.parent.callee&&pa(a.parent.callee,"forwardRef"))}function ua(a){return!!(a.parent&&a.parent.callee&&pa(a.parent.callee,"memo"))}function va(a){for(;a;){var d=wa(a);if(d&&(oa(d)||na(d))||ta(a)||ua(a))return!0;a=a.parent}return!1}
function wa(a){if("FunctionDeclaration"===a.type||"FunctionExpression"===a.type&&a.id)return a.id;if("FunctionExpression"===a.type||"ArrowFunctionExpression"===a.type)return"VariableDeclarator"===a.parent.type&&a.parent.init===a?a.parent.id:"AssignmentExpression"===a.parent.type&&a.parent.right===a&&"="===a.parent.operator?a.parent.left:"Property"!==a.parent.type||a.parent.value!==a||a.parent.computed?"AssignmentPattern"!==a.parent.type||a.parent.right!==a||a.parent.computed?void 0:a.parent.left:
a.parent.key}
function xa(a){function d(){return{isUsed:!1,isSatisfiedRecursively:!1,isSubtreeUsed:!1,children:new Map}}function f(k,m){m=m.split(".");m=P(m);var D;try{for(m.s();!(D=m.n()).done;){var F=D.value,G=k.children.get(F);G||(G=d(),k.children.set(F,G));k=G}}catch(I){m.e(I)}finally{m.f()}return k}function p(k,m,D){m=m.split(".");m=P(m);var F;try{for(m.s();!(F=m.n()).done;){var G=k.children.get(F.value);if(!G)break;D(G);k=G}}catch(I){m.e(I)}finally{m.f()}}function t(k,m,D,F){k.children.forEach(function(G,I){var S=
F(I);G.isSatisfiedRecursively?G.isSubtreeUsed&&D.add(S):G.isUsed?m.add(S):t(G,m,D,function(Q){return S+"."+Q})})}var H=a.dependencies,r=a.declaredDependencies,h=a.stableDependencies,x=a.externalDependencies,L=a.isEffect,C=d();H.forEach(function(k,m){f(C,m).isUsed=!0;p(C,m,function(D){D.isSubtreeUsed=!0})});r.forEach(function(k){f(C,k.key).isSatisfiedRecursively=!0});h.forEach(function(k){f(C,k).isSatisfiedRecursively=!0});a=new Set;var R=new Set;t(C,a,R,function(k){return k});var n=[],q=new Set,y=
new Set;r.forEach(function(k){k=k.key;R.has(k)?-1===n.indexOf(k)?n.push(k):y.add(k):!L||k.endsWith(".current")||x.has(k)?q.add(k):-1===n.indexOf(k)&&n.push(k)});a.forEach(function(k){n.push(k)});return{suggestedDependencies:n,unnecessaryDependencies:q,duplicateDependencies:y,missingDependencies:a}}
function V(a){switch(a.type){case "ObjectExpression":return"object";case "ArrayExpression":return"array";case "ArrowFunctionExpression":case "FunctionExpression":return"function";case "ClassExpression":return"class";case "ConditionalExpression":if(null!=V(a.consequent)||null!=V(a.alternate))return"conditional";break;case "LogicalExpression":if(null!=V(a.left)||null!=V(a.right))return"logical expression";break;case "JSXFragment":return"JSX fragment";case "JSXElement":return"JSX element";case "AssignmentExpression":if(null!=
V(a.right))return"assignment expression";break;case "NewExpression":return"object construction";case "Literal":if(a.value instanceof RegExp)return"regular expression";break;case "TypeCastExpression":return V(a.expression);case "TSAsExpression":return V(a.expression)}return null}
function ya(a){var d=a.declaredDependenciesNode,f=a.componentScope,p=a.scope;return a.declaredDependencies.map(function(t){var H=t.key;t=f.variables.find(function(x){return x.name===H});if(null==t)return null;var r=t.defs[0];if(null==r)return null;if("Variable"===r.type&&"VariableDeclarator"===r.node.type&&"Identifier"===r.node.id.type&&null!=r.node.init){var h=V(r.node.init);if(null!=h)return[t,h]}return"FunctionName"===r.type&&"FunctionDeclaration"===r.node.type?[t,"function"]:"ClassName"===r.type&&
"ClassDeclaration"===r.node.type?[t,"class"]:null}).filter(Boolean).map(function(t){var H=t[0];t=t[1];var r=H.defs[0];a:{for(var h=!1,x=0;x<H.references.length;x++){var L=H.references[x];if(L.writeExpr)if(h){H=!0;break a}else{h=!0;continue}for(var C=L.from;C!==p&&null!=C;)C=C.upper;if(C!==p&&!za(d,L.identifier)){H=!0;break a}}H=!1}return{construction:r,depType:t,isUsedOutsideOfHook:H}})}
function Aa(a){return"MemberExpression"!==a.parent.type&&"OptionalMemberExpression"!==a.parent.type||a.parent.object!==a||"current"===a.parent.property.name||a.parent.computed||null!=a.parent.parent&&("CallExpression"===a.parent.parent.type||"OptionalCallExpression"===a.parent.parent.type)&&a.parent.parent.callee===a.parent?"MemberExpression"===a.type&&a.parent&&"AssignmentExpression"===a.parent.type&&a.parent.left===a?a.object:a:Aa(a.parent)}
function Ba(a,d,f){d&&(a.optional?d.has(f)||d.set(f,!0):d.set(f,!1))}
function X(a,d){if("Identifier"===a.type||"JSXIdentifier"===a.type)return a=a.name,d&&d.set(a,!1),a;if("MemberExpression"!==a.type||a.computed){if("OptionalMemberExpression"!==a.type||a.computed){if("ChainExpression"!==a.type||a.computed)throw Error("Unsupported node type: "+a.type);a=a.expression;if("CallExpression"===a.type)throw Error("Unsupported node type: "+a.type);var f=X(a.object,d),p=X(a.property,null);f=f+"."+p;Ba(a,d,f);return f}f=X(a.object,d);p=X(a.property,null);f=f+"."+p;Ba(a,d,f);
return f}f=X(a.object,d);p=X(a.property,null);f=f+"."+p;Ba(a,d,f);return f}function Ca(a){return"MemberExpression"!==a.type||"Identifier"!==a.object.type||"React"!==a.object.name||"Identifier"!==a.property.type||a.computed?a:a.property}
function Fa(a,d){var f=Ca(a);if("Identifier"!==f.type)return-1;switch(f.name){case "useEffect":case "useLayoutEffect":case "useCallback":case "useMemo":return 0;case "useImperativeHandle":return 1;default:if(f===a&&d&&d.additionalHooks){try{var p=X(f,null)}catch(t){if(/Unsupported node type/.test(t.message))return 0;throw t;}return d.additionalHooks.test(p)?0:-1}return-1}}
function Ga(a,d){for(var f=[a],p=null;f.length;){p=f.shift();if(("Identifier"===p.type||"JSXIdentifier"===p.type)&&p.type===d.type&&p.name===d.name&&p.range[0]===d.range[0]&&p.range[1]===d.range[1])return p;if(za(p,d)){a=0;for(var t=Object.entries(p);a<t.length;a++){var H=t[a],r=H[1];"parent"!==H[0]&&(Ha(r)?(r.parent=p,f.push(r)):Array.isArray(r)&&r.forEach(function(h){Ha(h)&&(h.parent=p,f.push(h))}))}}}return null}
function Ia(a){for(var d="",f=0;f<a.length;f++)d+=a[f],0===f&&2===a.length?d+=" and ":f===a.length-2&&2<a.length?d+=", and ":f<a.length-1&&(d+=", ");return d}function Ha(a){return"object"===typeof a&&null!==a&&!Array.isArray(a)&&"string"===typeof a.type}function za(a,d){return a.range[0]<=d.range[0]&&a.range[1]>=d.range[1]}exports.configs={recommended:{plugins:["react-hooks"],rules:{"react-hooks/rules-of-hooks":"error","react-hooks/exhaustive-deps":"warn"}}};
exports.rules={"rules-of-hooks":{meta:{type:"problem",docs:{description:"enforces the Rules of Hooks",recommended:!0,url:"https://reactjs.org/docs/hooks-rules.html"}},create:function(a){function d(h){h=P(h.references);try{for(h.s();!h.n().done;);}catch(x){h.e(x)}finally{h.f()}}var f=null,p=[],t=[],H=new WeakSet,r={};return r.onCodePathSegmentStart=function(h){return t.push(h)},r.onCodePathSegmentEnd=function(){return t.pop()},r.onCodePathStart=function(){return p.push(new Map)},r.onCodePathEnd=function(h,
x){function L(l,A){var z=L.cache,u=z.get(l.id);A=new Set(A);if(A.has(l.id)){z=[].concat(A);l=z.slice(z.indexOf(l.id)+1);l=P(l);var E;try{for(l.s();!(E=l.n()).done;)q.add(E.value)}catch(M){l.e(M)}finally{l.f()}return BigInt("0")}A.add(l.id);if(void 0!==u)return u;if(h.thrownSegments.includes(l))u=BigInt("0");else if(0===l.prevSegments.length)u=BigInt("1");else{u=BigInt("0");E=P(l.prevSegments);var B;try{for(E.s();!(B=E.n()).done;)u+=L(B.value,A)}catch(M){E.e(M)}finally{E.f()}}l.reachable&&u===BigInt("0")?
z.delete(l.id):z.set(l.id,u);return u}function C(l,A){var z=C.cache,u=z.get(l.id);A=new Set(A);if(A.has(l.id)){z=Array.from(A);l=z.slice(z.indexOf(l.id)+1);l=P(l);var E;try{for(l.s();!(E=l.n()).done;)q.add(E.value)}catch(M){l.e(M)}finally{l.f()}return BigInt("0")}A.add(l.id);if(void 0!==u)return u;if(h.thrownSegments.includes(l))u=BigInt("0");else if(0===l.nextSegments.length)u=BigInt("1");else{u=BigInt("0");E=P(l.nextSegments);var B;try{for(E.s();!(B=E.n()).done;)u+=C(B.value,A)}catch(M){E.e(M)}finally{E.f()}}z.set(l.id,
u);return u}function R(l){var A=R.cache,z=A.get(l.id);if(null===z)return Infinity;if(void 0!==z)return z;A.set(l.id,null);if(0===l.prevSegments.length)z=1;else{z=Infinity;var u=P(l.prevSegments),E;try{for(u.s();!(E=u.n()).done;){var B=R(E.value);B<z&&(z=B)}}catch(M){u.e(M)}finally{u.f()}z+=1}A.set(l.id,z);return z}var n=p.pop();if(0!==n.size){var q=new Set;L.cache=new Map;C.cache=new Map;R.cache=new Map;var y=C(h.initialSegment),k=wa(x),m=va(x),D=k?oa(k)||na(k):ta(x)||ua(x),F=Infinity,G=P(h.finalSegments),
I;try{for(G.s();!(I=G.n()).done;){var S=I.value;if(S.reachable){var Q=R(S);Q<F&&(F=Q)}}}catch(l){G.e(l)}finally{G.f()}n=P(n);var N;try{for(n.s();!(N=n.n()).done;){var ca=N.value,T=ca[0],qa=ca[1];if(T.reachable){var ia=0===T.nextSegments.length?F<=R(T):F<R(T),O=L(T)*C(T),fa=q.has(T.id),Y=P(qa),Z;try{for(Y.s();!(Z=Y.n()).done;){var J=Z.value;fa&&a.report({node:J,message:'React Hook "'+a.getSource(J)+'" may be executed more than once. Possibly because it is called in a loop. React Hooks must be called in the exact same order in every component render.'});
if(D){if(x.async&&a.report({node:J,message:'React Hook "'+a.getSource(J)+'" cannot be called in an async function.'}),!fa&&O!==y){var aa='React Hook "'+a.getSource(J)+'" is called conditionally. React Hooks must be called in the exact same order in every component render.'+(ia?" Did you accidentally call a React Hook after an early return?":"");a.report({node:J,message:aa})}}else if(x.parent&&("MethodDefinition"===x.parent.type||"ClassProperty"===x.parent.type)&&x.parent.value===x){var K='React Hook "'+
a.getSource(J)+'" cannot be called in a class component. React Hooks must be called in a React function component or a custom React Hook function.';a.report({node:J,message:K})}else if(k){var U='React Hook "'+a.getSource(J)+'" is called in function "'+(a.getSource(k)+'" that is neither a React function component nor a custom React Hook function. React component names must start with an uppercase letter. React Hook names must start with the word "use".');a.report({node:J,message:U})}else if("Program"===
x.type){var ja='React Hook "'+a.getSource(J)+'" cannot be called at the top level. React Hooks must be called in a React function component or a custom React Hook function.';a.report({node:J,message:ja})}else if(m){var da='React Hook "'+a.getSource(J)+'" cannot be called inside a callback. React Hooks must be called in a React function component or a custom React Hook function.';a.report({node:J,message:da})}}}catch(l){Y.e(l)}finally{Y.f()}}}}catch(l){n.e(l)}finally{n.f()}}},r.CallExpression=function(h){if(na(h.callee)){var x=
p[p.length-1],L=t[t.length-1],C=x.get(L);C||(C=[],x.set(L,C));C.push(h.callee)}"Identifier"===h.callee.type&&"useEffect"===h.callee.name&&0<h.arguments.length&&(f=h)},r.Identifier=function(h){null==f&&H.has(h)&&"CallExpression"!==h.parent.type&&a.report({node:h,message:"`"+a.getSource(h)+'` is a function created with React Hook "useEffectEvent", and can only be called from the same component. They cannot be assigned to variables or passed down.'})},r["CallExpression:exit"]=function(h){h===f&&(f=null)},
r.FunctionDeclaration=function(h){va(h)&&d(a.getScope())},r.ArrowFunctionExpression=function(h){va(h)&&d(a.getScope())},r}},"exhaustive-deps":{meta:{type:"suggestion",docs:{description:"verifies the list of dependencies for Hooks like useEffect and similar",recommended:!0,url:"https://github.com/facebook/react/issues/14920"},fixable:"code",hasSuggestions:!0,schema:[{type:"object",additionalProperties:!1,enableDangerousAutofixThisMayCauseInfiniteLoops:!1,properties:{additionalHooks:{type:"string"},
enableDangerousAutofixThisMayCauseInfiniteLoops:{type:"boolean"}}}]},create:function(a){function d(n){t&&Array.isArray(n.suggest)&&0<n.suggest.length&&(n.fix=n.suggest[0].fix);a.report(n)}function f(n,q){return function(y){if(q.has(y))return q.get(y);var k=n(y);q.set(y,k);return k}}function p(n,q,y,k,m){function D(b){var e=P(b.references),c;try{for(e.s();!(c=e.n()).done;){var g=c.value;if(g.resolved&&S.has(g.resolved.scope)){var w=Ga(n,g.identifier),v=Aa(w),ba=X(v,fa),ea;if(ea=m&&"Identifier"===v.type&&
("MemberExpression"===v.parent.type||"OptionalMemberExpression"===v.parent.type)&&!v.parent.computed&&"Identifier"===v.parent.property.type&&"current"===v.parent.property.name){for(var W=g.from,ka=!1;W.block!==n;)"function"===W.type&&(ka=null!=W.block.parent&&"ReturnStatement"===W.block.parent.type),W=W.upper;ea=ka}ea&&ia.set(ba,{reference:g,dependencyNode:v});if("TSTypeQuery"!==v.parent.type&&"TSTypeReference"!==v.parent.type){var la=g.resolved.defs[0];if(null!=la&&(null==la.node||la.node.init!==
n.parent)&&"TypeParameter"!==la.type)if(O.has(ba))O.get(ba).references.push(g);else{var Da=g.resolved,Ja=T(Da)||qa(Da);O.set(ba,{isStable:Ja,references:[g]})}}}}}catch(ra){e.e(ra)}finally{e.f()}b=P(b.childScopes);var Ea;try{for(b.s();!(Ea=b.n()).done;)D(Ea.value)}catch(ra){b.e(ra)}finally{b.f()}}function F(b){b=b.split(".");for(var e="",c=0;c<b.length;c++){if(0!==c){var g=b.slice(0,c+1).join(".");g=!0===fa.get(g);e+=g?"?.":"."}e+=b[c]}return e}function G(b,e,c,g){return 0===b.size?null:(1<b.size?
"":e+" ")+c+" "+(1<b.size?"dependencies":"dependency")+": "+Ia(Array.from(b).sort().map(function(w){return"'"+F(w)+"'"}))+(". Either "+g+" "+(1<b.size?"them":"it")+" or remove the dependency array.")}m&&n.async&&d({node:n,message:"Effect callbacks are synchronous to prevent race conditions. Put the async function inside:\n\nuseEffect(() => {\n  async function fetchData() {\n    // You can await here\n    const response = await MyAPI.getData(someId);\n    // ...\n  }\n  fetchData();\n}, [someId]); // Or [] if effect doesn't need props or state\n\nLearn more about data fetching with Hooks: https://reactjs.org/link/hooks-data-fetching"});
for(var I=r.acquire(n),S=new Set,Q=null,N=I.upper;N;){S.add(N);if("function"===N.type)break;N=N.upper}if(N){Q=N;var ca=Array.isArray,T=f(function(b){if(!ca(b.defs))return!1;var e=b.defs[0];if(null==e||"VariableDeclarator"!==e.node.type)return!1;var c=e.node.init;if(null==c)return!1;for(;"TSAsExpression"===c.type;)c=c.expression;var g=e.node.parent;if(null==g&&(Ga(Q.block,e.node.id),g=e.node.parent,null==g))return!1;if("const"===g.kind&&"Literal"===c.type&&("string"===typeof c.value||"number"===typeof c.value||
null===c.value))return!0;if("CallExpression"!==c.type)return!1;c=c.callee;"MemberExpression"!==c.type||"React"!==c.object.name||null==c.property||c.computed||(c=c.property);if("Identifier"!==c.type)return!1;e=e.node.id;c=c.name;if("useRef"===c&&"Identifier"===e.type)return!0;if("useState"===c||"useReducer"===c){if("ArrayPattern"===e.type&&2===e.elements.length&&ca(b.identifiers)){if(e.elements[1]===b.identifiers[0]){if("useState"===c)for(b=b.references,g=c=0;g<b.length;g++){b[g].isWrite()&&c++;if(1<
c)return!1;h.set(b[g].identifier,e.elements[0])}return!0}if(e.elements[0]===b.identifiers[0]&&"useState"===c)for(b=b.references,e=0;e<b.length;e++)x.add(b[e].identifier)}}else if("useTransition"===c&&"ArrayPattern"===e.type&&2===e.elements.length&&Array.isArray(b.identifiers)&&e.elements[1]===b.identifiers[0])return!0;return!1},L),qa=f(function(b){if(!ca(b.defs))return!1;b=b.defs[0];if(null==b||null==b.node||null==b.node.id)return!1;var e=b.node,c=Q.childScopes;b=null;var g;for(g=0;g<c.length;g++){var w=
c[g],v=w.block;if("FunctionDeclaration"===e.type&&v===e||"VariableDeclarator"===e.type&&v.parent===e){b=w;break}}if(null==b)return!1;for(g=0;g<b.through.length;g++)if(e=b.through[g],null!=e.resolved&&S.has(e.resolved.scope)&&!T(e.resolved))return!1;return!0},C),ia=new Map,O=new Map,fa=new Map;D(I);ia.forEach(function(b,e){var c=b.dependencyNode;b=b.reference.resolved.references;for(var g=!1,w=0;w<b.length;w++){var v=b[w].identifier.parent;if(null!=v&&"MemberExpression"===v.type&&!v.computed&&"Identifier"===
v.property.type&&"current"===v.property.name&&"AssignmentExpression"===v.parent.type&&v.parent.left===v){g=!0;break}}g||d({node:c.parent.property,message:"The ref value '"+e+".current' will likely have changed by the time this effect cleanup function runs. If this ref points to a node rendered by React, copy '"+(e+".current' to a variable inside the effect, and use that variable in the cleanup function.")})});var Y=new Set,Z=new Set;O.forEach(function(b,e){var c=b.references;b.isStable&&Z.add(e);
c.forEach(function(g){g.writeExpr&&(g=g.writeExpr,Y.has(e)||(Y.add(e),d({node:g,message:"Assignments to the '"+e+"' variable from inside React Hook "+(a.getSource(y)+" will be lost after each render. To preserve the value over time, store it in a useRef Hook and keep the mutable value in the '.current' property. Otherwise, you can move this variable directly inside ")+(a.getSource(y)+".")})))})});if(!(0<Y.size))if(q){var J=[],aa=new Set;"ArrayExpression"!==q.type?d({node:q,message:"React Hook "+a.getSource(y)+
" was passed a dependency list that is not an array literal. This means we can't statically verify whether you've passed the correct dependencies."}):q.elements.forEach(function(b){if(null!==b)if("SpreadElement"===b.type)d({node:b,message:"React Hook "+a.getSource(y)+" has a spread element in its dependency array. This means we can't statically verify whether you've passed the correct dependencies."});else{R.has(b)&&d({node:b,message:"Functions returned from `useEffectEvent` must not be included in the dependency array. Remove `"+
(a.getSource(b)+"` from the list."),suggest:[{desc:"Remove the dependency `"+a.getSource(b)+"`",fix:function(w){return w.removeRange(b.range)}}]});try{var e=X(b,null)}catch(w){if(/Unsupported node type/.test(w.message)){"Literal"===b.type?O.has(b.value)?d({node:b,message:"The "+b.raw+" literal is not a valid dependency because it never changes. Did you mean to include "+(b.value+" in the array instead?")}):d({node:b,message:"The "+b.raw+" literal is not a valid dependency because it never changes. You can safely remove it."}):
d({node:b,message:"React Hook "+a.getSource(y)+" has a complex expression in the dependency array. Extract it to a separate variable so it can be statically checked."});return}throw w;}for(var c=b;"MemberExpression"===c.type||"OptionalMemberExpression"===c.type||"ChainExpression"===c.type;)c=c.object||c.expression.object;var g=!Q.through.some(function(w){return w.identifier===c});J.push({key:e,node:b});g||aa.add(e)}});var K=xa({dependencies:O,declaredDependencies:J,stableDependencies:Z,externalDependencies:aa,
isEffect:m});N=K.unnecessaryDependencies;var U=K.missingDependencies,ja=K.duplicateDependencies,da=K.suggestedDependencies;if(0===ja.size+U.size+N.size)ya({declaredDependencies:J,declaredDependenciesNode:q,componentScope:Q,scope:I}).forEach(function(b){var e=b.construction,c=b.isUsedOutsideOfHook;b=b.depType;var g="function"===b?"useCallback":"useMemo",w="function"===b?"definition":"initialization",v="wrap the "+w+" of '"+e.name.name+"' in its own "+g+"() Hook.";v="The '"+e.name.name+"' "+b+" "+("conditional"===
b||"logical expression"===b?"could make":"makes")+" the dependencies of "+(k+" Hook (at line "+q.loc.start.line+") change on every render. ")+(c?"To fix this, "+v:"Move it inside the "+k+" callback. Alternatively, "+v);var ba;c&&"Variable"===e.type&&"function"===b&&(ba=[{desc:"Wrap the "+w+" of '"+e.name.name+"' in its own "+g+"() Hook.",fix:function(ea){var W="useMemo"===g?["useMemo(() => { return ","; })"]:["useCallback(",")"],ka=W[1];return[ea.insertTextBefore(e.node.init,W[0]),ea.insertTextAfter(e.node.init,
ka)]}}]);d({node:e.node,message:v,suggest:ba})});else{!m&&0<U.size&&(da=xa({dependencies:O,declaredDependencies:[],stableDependencies:Z,externalDependencies:aa,isEffect:m}).suggestedDependencies);(function(){if(0===J.length)return!0;var b=J.map(function(c){return c.key}),e=b.slice().sort();return b.join(",")===e.join(",")})()&&da.sort();K="";if(0<N.size){var l=null;Array.from(N.keys()).forEach(function(b){null===l&&b.endsWith(".current")&&(l=b)});if(null!==l)K=" Mutable values like '"+l+"' aren't valid dependencies because mutating them doesn't re-render the component.";
else if(0<aa.size){var A=Array.from(aa)[0];I.set.has(A)||(K=" Outer scope values like '"+A+"' aren't valid dependencies because mutating them doesn't re-render the component.")}}if(!K&&U.has("props")){I=O.get("props");if(null==I)return;I=I.references;if(!Array.isArray(I))return;A=!0;for(var z=0;z<I.length;z++){var u=Ga(Q.block,I[z].identifier);if(!u){A=!1;break}u=u.parent;if(null==u){A=!1;break}if("MemberExpression"!==u.type&&"OptionalMemberExpression"!==u.type){A=!1;break}}A&&(K=" However, 'props' will change when *any* prop changes, so the preferred fix is to destructure the 'props' object outside of the "+
(k+" call and refer to those specific props inside ")+(a.getSource(y)+"."))}if(!K&&0<U.size){var E=null;U.forEach(function(b){if(!E){var e=Q.set.get(b),c=O.get(b);if(c.references[0].resolved===e&&(e=e.defs[0],null!=e&&null!=e.name&&"Parameter"===e.type)){e=!1;for(var g,w=0;w<c.references.length;w++)if(g=c.references[w].identifier,null!=g&&null!=g.parent&&("CallExpression"===g.parent.type||"OptionalCallExpression"===g.parent.type)&&g.parent.callee===g){e=!0;break}e&&(E=b)}}});null!==E&&(K=" If '"+
E+"' changes too often, find the parent component that defines it and wrap that definition in useCallback.")}if(!K&&0<U.size){var B=null;U.forEach(function(b){if(null===B)for(var e=O.get(b).references,c,g,w=0;w<e.length;w++){c=e[w].identifier;for(g=c.parent;null!=g&&g!==Q.block;){if("CallExpression"===g.type){var v=h.get(g.callee);if(null!=v){v.name===b?B={missingDep:b,setter:g.callee.name,form:"updater"}:x.has(c)?B={missingDep:b,setter:g.callee.name,form:"reducer"}:(c=e[w].resolved,null!=c&&(c=c.defs[0],
null!=c&&"Parameter"===c.type&&(B={missingDep:b,setter:g.callee.name,form:"inlineReducer"})));break}}g=g.parent}if(null!==B)break}});if(null!==B)switch(B.form){case "reducer":K=" You can also replace multiple useState variables with useReducer if '"+(B.setter+"' needs the current value of '")+(B.missingDep+"'.");break;case "inlineReducer":K=" If '"+B.setter+"' needs the current value of '"+(B.missingDep+"', you can also switch to useReducer instead of useState and read '")+(B.missingDep+"' in the reducer.");
break;case "updater":K=" You can also do a functional update '"+B.setter+"("+B.missingDep.slice(0,1)+" => ...)' if you only need '"+B.missingDep+"' in the '"+(B.setter+"' call.");break;default:throw Error("Unknown case.");}}d({node:q,message:"React Hook "+a.getSource(y)+" has "+(G(U,"a","missing","include")||G(N,"an","unnecessary","exclude")||G(ja,"a","duplicate","omit"))+K,suggest:[{desc:"Update the dependencies array to be: ["+da.map(F).join(", ")+"]",fix:function(b){return b.replaceText(q,"["+
da.map(F).join(", ")+"]")}}]})}}else{var M=null;O.forEach(function(b,e){M||b.references.forEach(function(c){if(!M&&h.has(c.identifier)){for(c=c.from;"function"!==c.type;)c=c.upper;c.block===n&&(M=e)}})});if(M){var sa=xa({dependencies:O,declaredDependencies:[],stableDependencies:Z,externalDependencies:new Set,isEffect:!0}).suggestedDependencies;d({node:y,message:"React Hook "+k+" contains a call to '"+M+"'. Without a list of dependencies, this can lead to an infinite chain of updates. To fix this, pass ["+
sa.join(", ")+("] as a second argument to the "+k+" Hook."),suggest:[{desc:"Add dependencies array: ["+sa.join(", ")+"]",fix:function(b){return b.insertTextAfter(n,", ["+sa.join(", ")+"]")}}]})}}}}var t=a.options&&a.options[0]&&a.options[0].enableDangerousAutofixThisMayCauseInfiniteLoops||!1,H={additionalHooks:a.options&&a.options[0]&&a.options[0].additionalHooks?new RegExp(a.options[0].additionalHooks):void 0,enableDangerousAutofixThisMayCauseInfiniteLoops:t},r=a.getSourceCode().scopeManager,h=new WeakMap,
x=new WeakSet,L=new WeakMap,C=new WeakMap,R=new WeakSet;return{CallExpression:function(n){var q=Fa(n.callee,H);if(-1!==q){var y=n.arguments[q],k=n.callee,m=Ca(k).name,D=n.arguments[q+1];n=/Effect($|[^a-z])/g.test(m);if(y)if(D||n){switch(y.type){case "FunctionExpression":case "ArrowFunctionExpression":p(y,D,k,m,n);return;case "Identifier":if(!D||D.elements&&D.elements.some(function(F){return F&&"Identifier"===F.type&&F.name===y.name}))return;q=a.getScope().set.get(y.name);if(null==q||null==q.defs)return;
q=q.defs[0];if(!q||!q.node)break;if("Variable"!==q.type&&"FunctionName"!==q.type)break;switch(q.node.type){case "FunctionDeclaration":p(q.node,D,k,m,n);return;case "VariableDeclarator":if(q=q.node.init)switch(q.type){case "ArrowFunctionExpression":case "FunctionExpression":p(q,D,k,m,n);return}}break;default:d({node:k,message:"React Hook "+m+" received a function whose dependencies are unknown. Pass an inline function instead."});return}d({node:k,message:"React Hook "+m+" has a missing dependency: '"+
y.name+"'. Either include it or remove the dependency array.",suggest:[{desc:"Update the dependencies array to be: ["+y.name+"]",fix:function(F){return F.replaceText(D,"["+y.name+"]")}}]})}else"useMemo"!==m&&"useCallback"!==m||d({node:k,message:"React Hook "+m+" does nothing when called with only one argument. Did you forget to pass an array of dependencies?"});else d({node:k,message:"React Hook "+m+" requires an effect callback. Did you forget to pass a callback to the hook?"})}}}}}};
