"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/page.js":
/*!*************************!*\
  !*** ./src/app/page.js ***!
  \*************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ AdminHome; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/../node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/../node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction AdminHome() {\n    // Sample data for dashboard\n    const stats = {\n        totalProducts: 25,\n        totalOrders: 150,\n        totalCustomers: 89,\n        totalRevenue: 45000,\n        recentOrders: [\n            {\n                _id: \"1\",\n                customer: {\n                    name: \"أحمد محمد\"\n                },\n                items: [\n                    {\n                        product: {\n                            name: \"كرسي مكتب\"\n                        }\n                    }\n                ],\n                totalAmount: 850,\n                status: \"delivered\"\n            },\n            {\n                _id: \"2\",\n                customer: {\n                    name: \"فاطمة علي\"\n                },\n                items: [\n                    {\n                        product: {\n                            name: \"طاولة طعام\"\n                        }\n                    }\n                ],\n                totalAmount: 1200,\n                status: \"pending\"\n            },\n            {\n                _id: \"3\",\n                customer: {\n                    name: \"محمد سالم\"\n                },\n                items: [\n                    {\n                        product: {\n                            name: \"سرير مزدوج\"\n                        }\n                    }\n                ],\n                totalAmount: 2500,\n                status: \"processing\"\n            }\n        ],\n        topProducts: [\n            {\n                product: {\n                    name: \"كرسي مكتب مريح\"\n                },\n                totalSold: 45,\n                revenue: 38250\n            },\n            {\n                product: {\n                    name: \"طاولة طعام خشبية\"\n                },\n                totalSold: 20,\n                revenue: 24000\n            }\n        ]\n    };\n    const statCards = [\n        {\n            title: \"إجمالي المنتجات\",\n            value: stats.totalProducts,\n            icon: FiPackage,\n            color: \"bg-blue-500\"\n        },\n        {\n            title: \"إجمالي الطلبات\",\n            value: stats.totalOrders,\n            icon: FiShoppingCart,\n            color: \"bg-green-500\"\n        },\n        {\n            title: \"إجمالي العملاء\",\n            value: stats.totalCustomers,\n            icon: FiUsers,\n            color: \"bg-purple-500\"\n        },\n        {\n            title: \"إجمالي الإيرادات\",\n            value: \"\".concat(stats.totalRevenue.toLocaleString(), \" ر.س\"),\n            icon: FiDollarSign,\n            color: \"bg-yellow-500\"\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                className: \"text-3xl font-bold text-gray-800 mb-8\",\n                children: \"لوحة التحكم\"\n            }, void 0, false, {\n                fileName: \"E:\\\\d1\\\\admin\\\\src\\\\app\\\\page.js\",\n                lineNumber: 78,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"dashboard-grid mb-8\",\n                children: statCards.map((stat, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"stats-card\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm font-medium text-gray-600\",\n                                            children: stat.title\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\d1\\\\admin\\\\src\\\\app\\\\page.js\",\n                                            lineNumber: 86,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-2xl font-bold text-gray-900\",\n                                            children: stat.value\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\d1\\\\admin\\\\src\\\\app\\\\page.js\",\n                                            lineNumber: 87,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\d1\\\\admin\\\\src\\\\app\\\\page.js\",\n                                    lineNumber: 85,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"stats-card-icon \".concat(stat.color),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(stat.icon, {\n                                        size: 24\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\d1\\\\admin\\\\src\\\\app\\\\page.js\",\n                                        lineNumber: 90,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\d1\\\\admin\\\\src\\\\app\\\\page.js\",\n                                    lineNumber: 89,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\d1\\\\admin\\\\src\\\\app\\\\page.js\",\n                            lineNumber: 84,\n                            columnNumber: 13\n                        }, this)\n                    }, index, false, {\n                        fileName: \"E:\\\\d1\\\\admin\\\\src\\\\app\\\\page.js\",\n                        lineNumber: 83,\n                        columnNumber: 11\n                    }, this))\n            }, void 0, false, {\n                fileName: \"E:\\\\d1\\\\admin\\\\src\\\\app\\\\page.js\",\n                lineNumber: 81,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 lg:grid-cols-2 gap-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"card\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"card-header\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-lg font-semibold\",\n                                    children: \"الطلبات الأخيرة\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\d1\\\\admin\\\\src\\\\app\\\\page.js\",\n                                    lineNumber: 101,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\d1\\\\admin\\\\src\\\\app\\\\page.js\",\n                                lineNumber: 100,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"card-body\",\n                                children: stats.recentOrders.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: stats.recentOrders.map((order)=>{\n                                        var _order_customer;\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between p-3 bg-gray-50 rounded-lg\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"font-medium\",\n                                                            children: (_order_customer = order.customer) === null || _order_customer === void 0 ? void 0 : _order_customer.name\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\d1\\\\admin\\\\src\\\\app\\\\page.js\",\n                                                            lineNumber: 109,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-600\",\n                                                            children: [\n                                                                order.items.length,\n                                                                \" منتج - \",\n                                                                order.totalAmount.toLocaleString(),\n                                                                \" ر.س\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\d1\\\\admin\\\\src\\\\app\\\\page.js\",\n                                                            lineNumber: 110,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\d1\\\\admin\\\\src\\\\app\\\\page.js\",\n                                                    lineNumber: 108,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"px-2 py-1 text-xs rounded-full \".concat(order.status === \"delivered\" ? \"bg-green-100 text-green-800\" : order.status === \"pending\" ? \"bg-yellow-100 text-yellow-800\" : \"bg-blue-100 text-blue-800\"),\n                                                    children: order.status === \"delivered\" ? \"تم التسليم\" : order.status === \"pending\" ? \"في الانتظار\" : \"قيد المعالجة\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\d1\\\\admin\\\\src\\\\app\\\\page.js\",\n                                                    lineNumber: 114,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, order._id, true, {\n                                            fileName: \"E:\\\\d1\\\\admin\\\\src\\\\app\\\\page.js\",\n                                            lineNumber: 107,\n                                            columnNumber: 19\n                                        }, this);\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\d1\\\\admin\\\\src\\\\app\\\\page.js\",\n                                    lineNumber: 105,\n                                    columnNumber: 15\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-500 text-center py-4\",\n                                    children: \"لا توجد طلبات حديثة\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\d1\\\\admin\\\\src\\\\app\\\\page.js\",\n                                    lineNumber: 126,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\d1\\\\admin\\\\src\\\\app\\\\page.js\",\n                                lineNumber: 103,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\d1\\\\admin\\\\src\\\\app\\\\page.js\",\n                        lineNumber: 99,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"card\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"card-header\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-lg font-semibold\",\n                                    children: \"المنتجات الأكثر مبيعاً\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\d1\\\\admin\\\\src\\\\app\\\\page.js\",\n                                    lineNumber: 134,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\d1\\\\admin\\\\src\\\\app\\\\page.js\",\n                                lineNumber: 133,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"card-body\",\n                                children: stats.topProducts.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: stats.topProducts.map((item, index)=>{\n                                        var _item_product, _item_revenue;\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between p-3 bg-gray-50 rounded-lg\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"font-medium\",\n                                                            children: (_item_product = item.product) === null || _item_product === void 0 ? void 0 : _item_product.name\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\d1\\\\admin\\\\src\\\\app\\\\page.js\",\n                                                            lineNumber: 142,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-600\",\n                                                            children: [\n                                                                item.totalSold,\n                                                                \" قطعة مباعة\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\d1\\\\admin\\\\src\\\\app\\\\page.js\",\n                                                            lineNumber: 143,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\d1\\\\admin\\\\src\\\\app\\\\page.js\",\n                                                    lineNumber: 141,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-right\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"font-bold text-green-600\",\n                                                        children: [\n                                                            (_item_revenue = item.revenue) === null || _item_revenue === void 0 ? void 0 : _item_revenue.toLocaleString(),\n                                                            \" ر.س\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\d1\\\\admin\\\\src\\\\app\\\\page.js\",\n                                                        lineNumber: 148,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\d1\\\\admin\\\\src\\\\app\\\\page.js\",\n                                                    lineNumber: 147,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, index, true, {\n                                            fileName: \"E:\\\\d1\\\\admin\\\\src\\\\app\\\\page.js\",\n                                            lineNumber: 140,\n                                            columnNumber: 19\n                                        }, this);\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\d1\\\\admin\\\\src\\\\app\\\\page.js\",\n                                    lineNumber: 138,\n                                    columnNumber: 15\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-500 text-center py-4\",\n                                    children: \"لا توجد بيانات مبيعات\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\d1\\\\admin\\\\src\\\\app\\\\page.js\",\n                                    lineNumber: 156,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\d1\\\\admin\\\\src\\\\app\\\\page.js\",\n                                lineNumber: 136,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\d1\\\\admin\\\\src\\\\app\\\\page.js\",\n                        lineNumber: 132,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\d1\\\\admin\\\\src\\\\app\\\\page.js\",\n                lineNumber: 97,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\d1\\\\admin\\\\src\\\\app\\\\page.js\",\n        lineNumber: 77,\n        columnNumber: 5\n    }, this);\n}\n_c = AdminHome;\nvar _c;\n$RefreshReg$(_c, \"AdminHome\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvcGFnZS5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7OztBQUU0QztBQUU3QixTQUFTRTtJQUN0Qiw0QkFBNEI7SUFDNUIsTUFBTUMsUUFBUTtRQUNaQyxlQUFlO1FBQ2ZDLGFBQWE7UUFDYkMsZ0JBQWdCO1FBQ2hCQyxjQUFjO1FBQ2RDLGNBQWM7WUFDWjtnQkFDRUMsS0FBSztnQkFDTEMsVUFBVTtvQkFBRUMsTUFBTTtnQkFBWTtnQkFDOUJDLE9BQU87b0JBQUM7d0JBQUVDLFNBQVM7NEJBQUVGLE1BQU07d0JBQVk7b0JBQUU7aUJBQUU7Z0JBQzNDRyxhQUFhO2dCQUNiQyxRQUFRO1lBQ1Y7WUFDQTtnQkFDRU4sS0FBSztnQkFDTEMsVUFBVTtvQkFBRUMsTUFBTTtnQkFBWTtnQkFDOUJDLE9BQU87b0JBQUM7d0JBQUVDLFNBQVM7NEJBQUVGLE1BQU07d0JBQWE7b0JBQUU7aUJBQUU7Z0JBQzVDRyxhQUFhO2dCQUNiQyxRQUFRO1lBQ1Y7WUFDQTtnQkFDRU4sS0FBSztnQkFDTEMsVUFBVTtvQkFBRUMsTUFBTTtnQkFBWTtnQkFDOUJDLE9BQU87b0JBQUM7d0JBQUVDLFNBQVM7NEJBQUVGLE1BQU07d0JBQWE7b0JBQUU7aUJBQUU7Z0JBQzVDRyxhQUFhO2dCQUNiQyxRQUFRO1lBQ1Y7U0FDRDtRQUNEQyxhQUFhO1lBQ1g7Z0JBQ0VILFNBQVM7b0JBQUVGLE1BQU07Z0JBQWlCO2dCQUNsQ00sV0FBVztnQkFDWEMsU0FBUztZQUNYO1lBQ0E7Z0JBQ0VMLFNBQVM7b0JBQUVGLE1BQU07Z0JBQW1CO2dCQUNwQ00sV0FBVztnQkFDWEMsU0FBUztZQUNYO1NBQ0Q7SUFDSDtJQUVBLE1BQU1DLFlBQVk7UUFDaEI7WUFDRUMsT0FBTztZQUNQQyxPQUFPbEIsTUFBTUMsYUFBYTtZQUMxQmtCLE1BQU1DO1lBQ05DLE9BQU87UUFDVDtRQUNBO1lBQ0VKLE9BQU87WUFDUEMsT0FBT2xCLE1BQU1FLFdBQVc7WUFDeEJpQixNQUFNRztZQUNORCxPQUFPO1FBQ1Q7UUFDQTtZQUNFSixPQUFPO1lBQ1BDLE9BQU9sQixNQUFNRyxjQUFjO1lBQzNCZ0IsTUFBTUk7WUFDTkYsT0FBTztRQUNUO1FBQ0E7WUFDRUosT0FBTztZQUNQQyxPQUFPLEdBQXVDLE9BQXBDbEIsTUFBTUksWUFBWSxDQUFDb0IsY0FBYyxJQUFHO1lBQzlDTCxNQUFNTTtZQUNOSixPQUFPO1FBQ1Q7S0FDRDtJQUVELHFCQUNFLDhEQUFDSzs7MEJBQ0MsOERBQUNDO2dCQUFHQyxXQUFVOzBCQUF3Qzs7Ozs7OzBCQUd0RCw4REFBQ0Y7Z0JBQUlFLFdBQVU7MEJBQ1paLFVBQVVhLEdBQUcsQ0FBQyxDQUFDQyxNQUFNQyxzQkFDcEIsOERBQUNMO3dCQUFnQkUsV0FBVTtrQ0FDekIsNEVBQUNGOzRCQUFJRSxXQUFVOzs4Q0FDYiw4REFBQ0Y7O3NEQUNDLDhEQUFDTTs0Q0FBRUosV0FBVTtzREFBcUNFLEtBQUtiLEtBQUs7Ozs7OztzREFDNUQsOERBQUNlOzRDQUFFSixXQUFVO3NEQUFvQ0UsS0FBS1osS0FBSzs7Ozs7Ozs7Ozs7OzhDQUU3RCw4REFBQ1E7b0NBQUlFLFdBQVcsbUJBQThCLE9BQVhFLEtBQUtULEtBQUs7OENBQzNDLDRFQUFDUyxLQUFLWCxJQUFJO3dDQUFDYyxNQUFNOzs7Ozs7Ozs7Ozs7Ozs7Ozt1QkFQYkY7Ozs7Ozs7Ozs7MEJBY2QsOERBQUNMO2dCQUFJRSxXQUFVOztrQ0FFYiw4REFBQ0Y7d0JBQUlFLFdBQVU7OzBDQUNiLDhEQUFDRjtnQ0FBSUUsV0FBVTswQ0FDYiw0RUFBQ007b0NBQUdOLFdBQVU7OENBQXdCOzs7Ozs7Ozs7OzswQ0FFeEMsOERBQUNGO2dDQUFJRSxXQUFVOzBDQUNaNUIsTUFBTUssWUFBWSxDQUFDOEIsTUFBTSxHQUFHLGtCQUMzQiw4REFBQ1Q7b0NBQUlFLFdBQVU7OENBQ1o1QixNQUFNSyxZQUFZLENBQUN3QixHQUFHLENBQUMsQ0FBQ087NENBR1NBOzZEQUZoQyw4REFBQ1Y7NENBQW9CRSxXQUFVOzs4REFDN0IsOERBQUNGOztzRUFDQyw4REFBQ007NERBQUVKLFdBQVU7dUVBQWVRLGtCQUFBQSxNQUFNN0IsUUFBUSxjQUFkNkIsc0NBQUFBLGdCQUFnQjVCLElBQUk7Ozs7OztzRUFDaEQsOERBQUN3Qjs0REFBRUosV0FBVTs7Z0VBQ1ZRLE1BQU0zQixLQUFLLENBQUMwQixNQUFNO2dFQUFDO2dFQUFTQyxNQUFNekIsV0FBVyxDQUFDYSxjQUFjO2dFQUFHOzs7Ozs7Ozs7Ozs7OzhEQUdwRSw4REFBQ2E7b0RBQUtULFdBQVcsa0NBSWhCLE9BSENRLE1BQU14QixNQUFNLEtBQUssY0FBYyxnQ0FDL0J3QixNQUFNeEIsTUFBTSxLQUFLLFlBQVksa0NBQzdCOzhEQUVDd0IsTUFBTXhCLE1BQU0sS0FBSyxjQUFjLGVBQy9Cd0IsTUFBTXhCLE1BQU0sS0FBSyxZQUFZLGdCQUFnQjs7Ozs7OzsyQ0FieEN3QixNQUFNOUIsR0FBRzs7Ozs7Ozs7Ozt5REFtQnZCLDhEQUFDMEI7b0NBQUVKLFdBQVU7OENBQWlDOzs7Ozs7Ozs7Ozs7Ozs7OztrQ0FNcEQsOERBQUNGO3dCQUFJRSxXQUFVOzswQ0FDYiw4REFBQ0Y7Z0NBQUlFLFdBQVU7MENBQ2IsNEVBQUNNO29DQUFHTixXQUFVOzhDQUF3Qjs7Ozs7Ozs7Ozs7MENBRXhDLDhEQUFDRjtnQ0FBSUUsV0FBVTswQ0FDWjVCLE1BQU1hLFdBQVcsQ0FBQ3NCLE1BQU0sR0FBRyxrQkFDMUIsOERBQUNUO29DQUFJRSxXQUFVOzhDQUNaNUIsTUFBTWEsV0FBVyxDQUFDZ0IsR0FBRyxDQUFDLENBQUNTLE1BQU1QOzRDQUdJTyxlQU96QkE7NkRBVFAsOERBQUNaOzRDQUFnQkUsV0FBVTs7OERBQ3pCLDhEQUFDRjs7c0VBQ0MsOERBQUNNOzREQUFFSixXQUFVO3VFQUFlVSxnQkFBQUEsS0FBSzVCLE9BQU8sY0FBWjRCLG9DQUFBQSxjQUFjOUIsSUFBSTs7Ozs7O3NFQUM5Qyw4REFBQ3dCOzREQUFFSixXQUFVOztnRUFDVlUsS0FBS3hCLFNBQVM7Z0VBQUM7Ozs7Ozs7Ozs7Ozs7OERBR3BCLDhEQUFDWTtvREFBSUUsV0FBVTs4REFDYiw0RUFBQ0k7d0RBQUVKLFdBQVU7OzZEQUNWVSxnQkFBQUEsS0FBS3ZCLE9BQU8sY0FBWnVCLG9DQUFBQSxjQUFjZCxjQUFjOzREQUFHOzs7Ozs7Ozs7Ozs7OzJDQVQ1Qk87Ozs7Ozs7Ozs7eURBZ0JkLDhEQUFDQztvQ0FBRUosV0FBVTs4Q0FBaUM7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBTzVEO0tBOUp3QjdCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL3NyYy9hcHAvcGFnZS5qcz8yYjNkIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50JztcclxuXHJcbmltcG9ydCB7IHVzZVN0YXRlLCB1c2VFZmZlY3QgfSBmcm9tICdyZWFjdCc7XHJcblxyXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBBZG1pbkhvbWUoKSB7XHJcbiAgLy8gU2FtcGxlIGRhdGEgZm9yIGRhc2hib2FyZFxyXG4gIGNvbnN0IHN0YXRzID0ge1xyXG4gICAgdG90YWxQcm9kdWN0czogMjUsXHJcbiAgICB0b3RhbE9yZGVyczogMTUwLFxyXG4gICAgdG90YWxDdXN0b21lcnM6IDg5LFxyXG4gICAgdG90YWxSZXZlbnVlOiA0NTAwMCxcclxuICAgIHJlY2VudE9yZGVyczogW1xyXG4gICAgICB7XHJcbiAgICAgICAgX2lkOiAnMScsXHJcbiAgICAgICAgY3VzdG9tZXI6IHsgbmFtZTogJ9ij2K3ZhdivINmF2K3ZhdivJyB9LFxyXG4gICAgICAgIGl0ZW1zOiBbeyBwcm9kdWN0OiB7IG5hbWU6ICfZg9ix2LPZiiDZhdmD2KrYqCcgfSB9XSxcclxuICAgICAgICB0b3RhbEFtb3VudDogODUwLFxyXG4gICAgICAgIHN0YXR1czogJ2RlbGl2ZXJlZCdcclxuICAgICAgfSxcclxuICAgICAge1xyXG4gICAgICAgIF9pZDogJzInLFxyXG4gICAgICAgIGN1c3RvbWVyOiB7IG5hbWU6ICfZgdin2LfZhdipINi52YTZiicgfSxcclxuICAgICAgICBpdGVtczogW3sgcHJvZHVjdDogeyBuYW1lOiAn2LfYp9mI2YTYqSDYt9i52KfZhScgfSB9XSxcclxuICAgICAgICB0b3RhbEFtb3VudDogMTIwMCxcclxuICAgICAgICBzdGF0dXM6ICdwZW5kaW5nJ1xyXG4gICAgICB9LFxyXG4gICAgICB7XHJcbiAgICAgICAgX2lkOiAnMycsXHJcbiAgICAgICAgY3VzdG9tZXI6IHsgbmFtZTogJ9mF2K3ZhdivINiz2KfZhNmFJyB9LFxyXG4gICAgICAgIGl0ZW1zOiBbeyBwcm9kdWN0OiB7IG5hbWU6ICfYs9ix2YrYsSDZhdiy2K/ZiNisJyB9IH1dLFxyXG4gICAgICAgIHRvdGFsQW1vdW50OiAyNTAwLFxyXG4gICAgICAgIHN0YXR1czogJ3Byb2Nlc3NpbmcnXHJcbiAgICAgIH1cclxuICAgIF0sXHJcbiAgICB0b3BQcm9kdWN0czogW1xyXG4gICAgICB7XHJcbiAgICAgICAgcHJvZHVjdDogeyBuYW1lOiAn2YPYsdiz2Yog2YXZg9iq2Kgg2YXYsdmK2K0nIH0sXHJcbiAgICAgICAgdG90YWxTb2xkOiA0NSxcclxuICAgICAgICByZXZlbnVlOiAzODI1MFxyXG4gICAgICB9LFxyXG4gICAgICB7XHJcbiAgICAgICAgcHJvZHVjdDogeyBuYW1lOiAn2LfYp9mI2YTYqSDYt9i52KfZhSDYrti02KjZitipJyB9LFxyXG4gICAgICAgIHRvdGFsU29sZDogMjAsXHJcbiAgICAgICAgcmV2ZW51ZTogMjQwMDBcclxuICAgICAgfVxyXG4gICAgXVxyXG4gIH07XHJcblxyXG4gIGNvbnN0IHN0YXRDYXJkcyA9IFtcclxuICAgIHtcclxuICAgICAgdGl0bGU6ICfYpdis2YXYp9mE2Yog2KfZhNmF2YbYqtis2KfYqicsXHJcbiAgICAgIHZhbHVlOiBzdGF0cy50b3RhbFByb2R1Y3RzLFxyXG4gICAgICBpY29uOiBGaVBhY2thZ2UsXHJcbiAgICAgIGNvbG9yOiAnYmctYmx1ZS01MDAnXHJcbiAgICB9LFxyXG4gICAge1xyXG4gICAgICB0aXRsZTogJ9il2KzZhdin2YTZiiDYp9mE2LfZhNio2KfYqicsXHJcbiAgICAgIHZhbHVlOiBzdGF0cy50b3RhbE9yZGVycyxcclxuICAgICAgaWNvbjogRmlTaG9wcGluZ0NhcnQsXHJcbiAgICAgIGNvbG9yOiAnYmctZ3JlZW4tNTAwJ1xyXG4gICAgfSxcclxuICAgIHtcclxuICAgICAgdGl0bGU6ICfYpdis2YXYp9mE2Yog2KfZhNi52YXZhNin2KEnLFxyXG4gICAgICB2YWx1ZTogc3RhdHMudG90YWxDdXN0b21lcnMsXHJcbiAgICAgIGljb246IEZpVXNlcnMsXHJcbiAgICAgIGNvbG9yOiAnYmctcHVycGxlLTUwMCdcclxuICAgIH0sXHJcbiAgICB7XHJcbiAgICAgIHRpdGxlOiAn2KXYrNmF2KfZhNmKINin2YTYpdmK2LHYp9iv2KfYqicsXHJcbiAgICAgIHZhbHVlOiBgJHtzdGF0cy50b3RhbFJldmVudWUudG9Mb2NhbGVTdHJpbmcoKX0g2LEu2LNgLFxyXG4gICAgICBpY29uOiBGaURvbGxhclNpZ24sXHJcbiAgICAgIGNvbG9yOiAnYmcteWVsbG93LTUwMCdcclxuICAgIH1cclxuICBdO1xyXG5cclxuICByZXR1cm4gKFxyXG4gICAgPGRpdj5cclxuICAgICAgPGgxIGNsYXNzTmFtZT1cInRleHQtM3hsIGZvbnQtYm9sZCB0ZXh0LWdyYXktODAwIG1iLThcIj7ZhNmI2K3YqSDYp9mE2KrYrdmD2YU8L2gxPlxyXG5cclxuICAgICAgey8qIFN0YXRzIENhcmRzICovfVxyXG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImRhc2hib2FyZC1ncmlkIG1iLThcIj5cclxuICAgICAgICB7c3RhdENhcmRzLm1hcCgoc3RhdCwgaW5kZXgpID0+IChcclxuICAgICAgICAgIDxkaXYga2V5PXtpbmRleH0gY2xhc3NOYW1lPVwic3RhdHMtY2FyZFwiPlxyXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlblwiPlxyXG4gICAgICAgICAgICAgIDxkaXY+XHJcbiAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS02MDBcIj57c3RhdC50aXRsZX08L3A+XHJcbiAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LTJ4bCBmb250LWJvbGQgdGV4dC1ncmF5LTkwMFwiPntzdGF0LnZhbHVlfTwvcD5cclxuICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT17YHN0YXRzLWNhcmQtaWNvbiAke3N0YXQuY29sb3J9YH0+XHJcbiAgICAgICAgICAgICAgICA8c3RhdC5pY29uIHNpemU9ezI0fSAvPlxyXG4gICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICkpfVxyXG4gICAgICA8L2Rpdj5cclxuXHJcbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMSBsZzpncmlkLWNvbHMtMiBnYXAtOFwiPlxyXG4gICAgICAgIHsvKiBSZWNlbnQgT3JkZXJzICovfVxyXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiY2FyZFwiPlxyXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJjYXJkLWhlYWRlclwiPlxyXG4gICAgICAgICAgICA8aDIgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LXNlbWlib2xkXCI+2KfZhNi32YTYqNin2Kog2KfZhNij2K7Zitix2Kk8L2gyPlxyXG4gICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImNhcmQtYm9keVwiPlxyXG4gICAgICAgICAgICB7c3RhdHMucmVjZW50T3JkZXJzLmxlbmd0aCA+IDAgPyAoXHJcbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTRcIj5cclxuICAgICAgICAgICAgICAgIHtzdGF0cy5yZWNlbnRPcmRlcnMubWFwKChvcmRlcikgPT4gKFxyXG4gICAgICAgICAgICAgICAgICA8ZGl2IGtleT17b3JkZXIuX2lkfSBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW4gcC0zIGJnLWdyYXktNTAgcm91bmRlZC1sZ1wiPlxyXG4gICAgICAgICAgICAgICAgICAgIDxkaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJmb250LW1lZGl1bVwiPntvcmRlci5jdXN0b21lcj8ubmFtZX08L3A+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS02MDBcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAge29yZGVyLml0ZW1zLmxlbmd0aH0g2YXZhtiq2KwgLSB7b3JkZXIudG90YWxBbW91bnQudG9Mb2NhbGVTdHJpbmcoKX0g2LEu2LNcclxuICAgICAgICAgICAgICAgICAgICAgIDwvcD5cclxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9e2BweC0yIHB5LTEgdGV4dC14cyByb3VuZGVkLWZ1bGwgJHtcclxuICAgICAgICAgICAgICAgICAgICAgIG9yZGVyLnN0YXR1cyA9PT0gJ2RlbGl2ZXJlZCcgPyAnYmctZ3JlZW4tMTAwIHRleHQtZ3JlZW4tODAwJyA6XHJcbiAgICAgICAgICAgICAgICAgICAgICBvcmRlci5zdGF0dXMgPT09ICdwZW5kaW5nJyA/ICdiZy15ZWxsb3ctMTAwIHRleHQteWVsbG93LTgwMCcgOlxyXG4gICAgICAgICAgICAgICAgICAgICAgJ2JnLWJsdWUtMTAwIHRleHQtYmx1ZS04MDAnXHJcbiAgICAgICAgICAgICAgICAgICAgfWB9PlxyXG4gICAgICAgICAgICAgICAgICAgICAge29yZGVyLnN0YXR1cyA9PT0gJ2RlbGl2ZXJlZCcgPyAn2KrZhSDYp9mE2KrYs9mE2YrZhScgOlxyXG4gICAgICAgICAgICAgICAgICAgICAgIG9yZGVyLnN0YXR1cyA9PT0gJ3BlbmRpbmcnID8gJ9mB2Yog2KfZhNin2YbYqti42KfYsScgOiAn2YLZitivINin2YTZhdi52KfZhNis2KknfVxyXG4gICAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cclxuICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICApKX1cclxuICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgKSA6IChcclxuICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNTAwIHRleHQtY2VudGVyIHB5LTRcIj7ZhNinINiq2YjYrNivINi32YTYqNin2Kog2K3Yr9mK2KvYqTwvcD5cclxuICAgICAgICAgICAgKX1cclxuICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgIDwvZGl2PlxyXG5cclxuICAgICAgICB7LyogVG9wIFByb2R1Y3RzICovfVxyXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiY2FyZFwiPlxyXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJjYXJkLWhlYWRlclwiPlxyXG4gICAgICAgICAgICA8aDIgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LXNlbWlib2xkXCI+2KfZhNmF2YbYqtis2KfYqiDYp9mE2KPZg9ir2LEg2YXYqNmK2LnYp9mLPC9oMj5cclxuICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJjYXJkLWJvZHlcIj5cclxuICAgICAgICAgICAge3N0YXRzLnRvcFByb2R1Y3RzLmxlbmd0aCA+IDAgPyAoXHJcbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTRcIj5cclxuICAgICAgICAgICAgICAgIHtzdGF0cy50b3BQcm9kdWN0cy5tYXAoKGl0ZW0sIGluZGV4KSA9PiAoXHJcbiAgICAgICAgICAgICAgICAgIDxkaXYga2V5PXtpbmRleH0gY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuIHAtMyBiZy1ncmF5LTUwIHJvdW5kZWQtbGdcIj5cclxuICAgICAgICAgICAgICAgICAgICA8ZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwiZm9udC1tZWRpdW1cIj57aXRlbS5wcm9kdWN0Py5uYW1lfTwvcD5cclxuICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTYwMFwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICB7aXRlbS50b3RhbFNvbGR9INmC2LfYudipINmF2KjYp9i52KlcclxuICAgICAgICAgICAgICAgICAgICAgIDwvcD5cclxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtcmlnaHRcIj5cclxuICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cImZvbnQtYm9sZCB0ZXh0LWdyZWVuLTYwMFwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICB7aXRlbS5yZXZlbnVlPy50b0xvY2FsZVN0cmluZygpfSDYsS7Ys1xyXG4gICAgICAgICAgICAgICAgICAgICAgPC9wPlxyXG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICkpfVxyXG4gICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICApIDogKFxyXG4gICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtZ3JheS01MDAgdGV4dC1jZW50ZXIgcHktNFwiPtmE2Kcg2KrZiNis2K8g2KjZitin2YbYp9iqINmF2KjZiti52KfYqjwvcD5cclxuICAgICAgICAgICAgKX1cclxuICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgIDwvZGl2PlxyXG4gICAgICA8L2Rpdj5cclxuICAgIDwvZGl2PlxyXG4gICk7XHJcbn0iXSwibmFtZXMiOlsidXNlU3RhdGUiLCJ1c2VFZmZlY3QiLCJBZG1pbkhvbWUiLCJzdGF0cyIsInRvdGFsUHJvZHVjdHMiLCJ0b3RhbE9yZGVycyIsInRvdGFsQ3VzdG9tZXJzIiwidG90YWxSZXZlbnVlIiwicmVjZW50T3JkZXJzIiwiX2lkIiwiY3VzdG9tZXIiLCJuYW1lIiwiaXRlbXMiLCJwcm9kdWN0IiwidG90YWxBbW91bnQiLCJzdGF0dXMiLCJ0b3BQcm9kdWN0cyIsInRvdGFsU29sZCIsInJldmVudWUiLCJzdGF0Q2FyZHMiLCJ0aXRsZSIsInZhbHVlIiwiaWNvbiIsIkZpUGFja2FnZSIsImNvbG9yIiwiRmlTaG9wcGluZ0NhcnQiLCJGaVVzZXJzIiwidG9Mb2NhbGVTdHJpbmciLCJGaURvbGxhclNpZ24iLCJkaXYiLCJoMSIsImNsYXNzTmFtZSIsIm1hcCIsInN0YXQiLCJpbmRleCIsInAiLCJzaXplIiwiaDIiLCJsZW5ndGgiLCJvcmRlciIsInNwYW4iLCJpdGVtIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/page.js\n"));

/***/ })

});