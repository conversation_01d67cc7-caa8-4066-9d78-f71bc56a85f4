'use client';

import { useState } from 'react';

export default function ProductsPage() {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('');

  // Sample data
  const products = [
    {
      _id: '1',
      name: 'كرسي مكتب مريح',
      description: 'كرسي مكتب عالي الجودة مع دعم للظهر',
      price: 850,
      category: 'مكتب',
      stock: 15,
      rating: 4.5
    },
    {
      _id: '2',
      name: 'طاولة طعام خشبية',
      description: 'طاولة طعام من الخشب الطبيعي تتسع لـ 6 أشخاص',
      price: 1200,
      category: 'طعام',
      stock: 8,
      rating: 4.8
    },
    {
      _id: '3',
      name: 'سرير مزدوج',
      description: 'سرير مزدوج مريح من الخشب الطبيعي',
      price: 2500,
      category: 'نوم',
      stock: 5,
      rating: 4.7
    },
    {
      _id: '4',
      name: 'خزانة ملابس',
      description: 'خزانة ملابس واسعة بثلاث أبواب',
      price: 1800,
      category: 'نوم',
      stock: 3,
      rating: 4.6
    }
  ];

  const categories = [
    { _id: '1', name: 'غرفة المعيشة' },
    { _id: '2', name: 'غرفة النوم' },
    { _id: '3', name: 'المكتب' },
    { _id: '4', name: 'غرفة الطعام' }
  ];

  const handleSearch = (e) => {
    e.preventDefault();
    // Handle search logic here
  };

  const clearFilters = () => {
    setSearchTerm('');
    setSelectedCategory('');
  };

  return (
    <div className="max-w-6xl mx-auto px-4 py-8">
      {/* Header */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-4">جميع المنتجات</h1>

        {/* Search Bar */}
        <form onSubmit={handleSearch} className="flex gap-2 mb-4">
          <div className="flex-1 relative">
            <input
              type="text"
              placeholder="ابحث عن المنتجات..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent pl-10"
            />
            <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400">🔍</span>
          </div>
          <button type="submit" className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors">
            بحث
          </button>
        </form>
      </div>

      <div className="flex flex-col md:flex-row gap-8">
        {/* Filters Sidebar */}
        <div className="md:w-64">
          <div className="bg-white p-6 rounded-lg shadow-md">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-semibold">الفلاتر</h3>
              <button
                onClick={clearFilters}
                className="text-blue-600 text-sm hover:underline"
              >
                مسح الكل
              </button>
            </div>

            {/* Category Filter */}
            <div className="mb-6">
              <label htmlFor="category" className="block text-sm font-medium text-gray-700 mb-2">الفئة</label>
              <select
                id="category"
                value={selectedCategory}
                onChange={(e) => setSelectedCategory(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="">جميع الفئات</option>
                {categories.map((category) => (
                  <option key={category._id} value={category._id}>
                    {category.name}
                  </option>
                ))}
              </select>
            </div>
          </div>
        </div>

        {/* Products Grid */}
        <div className="flex-1">
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
            {products.map((product) => (
              <div key={product._id} className="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow">
                <div className="h-48 bg-gray-200 flex items-center justify-center">
                  <span className="text-4xl">🪑</span>
                </div>

                <div className="p-4">
                  <h3 className="font-semibold text-lg mb-2">{product.name}</h3>
                  <p className="text-gray-600 text-sm mb-3">{product.description}</p>

                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center">
                      <span className="text-yellow-400">⭐</span>
                      <span className="text-gray-600 text-sm mr-1">
                        {product.rating ? product.rating.toFixed(1) : '0.0'}
                      </span>
                    </div>
                    <div className="text-xl font-bold text-blue-600">
                      {product.price.toLocaleString()} ر.س
                    </div>
                  </div>

                  <button className="w-full bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors">
                    عرض التفاصيل
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}
