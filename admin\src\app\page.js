'use client';

export default function AdminHome() {
  // Sample data for dashboard
  const stats = {
    totalProducts: 25,
    totalOrders: 150,
    totalCustomers: 89,
    totalRevenue: 45000,
    recentOrders: [
      {
        _id: '1',
        customer: { name: 'أحمد محمد' },
        items: [{ product: { name: 'كرسي مكتب' } }],
        totalAmount: 850,
        status: 'delivered'
      },
      {
        _id: '2',
        customer: { name: 'فاطمة علي' },
        items: [{ product: { name: 'طاولة طعام' } }],
        totalAmount: 1200,
        status: 'pending'
      },
      {
        _id: '3',
        customer: { name: 'محمد سالم' },
        items: [{ product: { name: 'سرير مزدوج' } }],
        totalAmount: 2500,
        status: 'processing'
      }
    ],
    topProducts: [
      {
        product: { name: 'كرسي مكتب مريح' },
        totalSold: 45,
        revenue: 38250
      },
      {
        product: { name: 'طاولة طعام خشبية' },
        totalSold: 20,
        revenue: 24000
      }
    ]
  };

  const statCards = [
    {
      title: 'إجمالي المنتجات',
      value: stats.totalProducts,
      icon: '📦',
      color: 'bg-blue-500'
    },
    {
      title: 'إجمالي الطلبات',
      value: stats.totalOrders,
      icon: '🛒',
      color: 'bg-green-500'
    },
    {
      title: 'إجمالي العملاء',
      value: stats.totalCustomers,
      icon: '👥',
      color: 'bg-purple-500'
    },
    {
      title: 'إجمالي الإيرادات',
      value: `${stats.totalRevenue.toLocaleString()} ر.س`,
      icon: '💰',
      color: 'bg-yellow-500'
    }
  ];

  return (
    <div>
      <h1 className="text-3xl font-bold text-gray-800 mb-8">لوحة التحكم</h1>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        {statCards.map((stat, index) => (
          <div key={`stat-${index}`} className="bg-white p-6 rounded-lg shadow-md">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">{stat.title}</p>
                <p className="text-2xl font-bold text-gray-900">{stat.value}</p>
              </div>
              <div className={`w-12 h-12 rounded-lg flex items-center justify-center text-white text-xl ${stat.color}`}>
                {stat.icon}
              </div>
            </div>
          </div>
        ))}
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Recent Orders */}
        <div className="bg-white rounded-lg shadow-md overflow-hidden">
          <div className="px-6 py-4 border-b border-gray-200">
            <h2 className="text-lg font-semibold">الطلبات الأخيرة</h2>
          </div>
          <div className="px-6 py-4">
            {stats.recentOrders.length > 0 ? (
              <div className="space-y-4">
                {stats.recentOrders.map((order) => {
                  const getStatusStyle = (status) => {
                    switch(status) {
                      case 'delivered': return 'bg-green-100 text-green-800';
                      case 'pending': return 'bg-yellow-100 text-yellow-800';
                      default: return 'bg-blue-100 text-blue-800';
                    }
                  };

                  const getStatusText = (status) => {
                    switch(status) {
                      case 'delivered': return 'تم التسليم';
                      case 'pending': return 'في الانتظار';
                      default: return 'قيد المعالجة';
                    }
                  };

                  return (
                    <div key={order._id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                      <div>
                        <p className="font-medium">{order.customer?.name}</p>
                        <p className="text-sm text-gray-600">
                          {order.items.length} منتج - {order.totalAmount.toLocaleString()} ر.س
                        </p>
                      </div>
                      <span className={`px-2 py-1 text-xs rounded-full ${getStatusStyle(order.status)}`}>
                        {getStatusText(order.status)}
                      </span>
                    </div>
                  );
                })}
              </div>
            ) : (
              <p className="text-gray-500 text-center py-4">لا توجد طلبات حديثة</p>
            )}
          </div>
        </div>

        {/* Top Products */}
        <div className="bg-white rounded-lg shadow-md overflow-hidden">
          <div className="px-6 py-4 border-b border-gray-200">
            <h2 className="text-lg font-semibold">المنتجات الأكثر مبيعاً</h2>
          </div>
          <div className="px-6 py-4">
            {stats.topProducts.length > 0 ? (
              <div className="space-y-4">
                {stats.topProducts.map((item, index) => (
                  <div key={`product-${index}`} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div>
                      <p className="font-medium">{item.product?.name}</p>
                      <p className="text-sm text-gray-600">
                        {item.totalSold} قطعة مباعة
                      </p>
                    </div>
                    <div className="text-right">
                      <p className="font-bold text-green-600">
                        {item.revenue?.toLocaleString()} ر.س
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <p className="text-gray-500 text-center py-4">لا توجد بيانات مبيعات</p>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}