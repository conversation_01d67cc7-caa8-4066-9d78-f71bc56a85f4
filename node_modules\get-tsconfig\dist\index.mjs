var de=Object.defineProperty;var o=(e,t)=>de(e,"name",{value:t,configurable:!0});import m from"node:path";import te from"node:fs";import ve from"node:module";import{resolveExports as Te}from"resolve-pkg-maps";import Ae from"fs";function E(e){return e.startsWith("\\\\?\\")?e:e.replace(/\\/g,"/")}o(E,"slash");const O=o(e=>{const t=te[e];return(s,...n)=>{const l=`${e}:${n.join(":")}`;let i=s==null?void 0:s.get(l);return i===void 0&&(i=Reflect.apply(t,te,n),s==null||s.set(l,i)),i}},"cacheFs"),B=O("existsSync"),_e=O("readFileSync"),P=O("statSync"),se=o((e,t,s)=>{for(;;){const n=m.posix.join(e,t);if(B(s,n))return n;const l=m.dirname(e);if(l===e)return;e=l}},"findUp"),z=/^\.{1,2}(\/.*)?$/,G=o(e=>{const t=E(e);return z.test(t)?t:`./${t}`},"normalizeRelativePath");function je(e,t=!1){const s=e.length;let n=0,l="",i=0,u=16,f=0,r=0,g=0,T=0,b=0;function _(c,k){let p=0,F=0;for(;p<c;){let j=e.charCodeAt(n);if(j>=48&&j<=57)F=F*16+j-48;else if(j>=65&&j<=70)F=F*16+j-65+10;else if(j>=97&&j<=102)F=F*16+j-97+10;else break;n++,p++}return p<c&&(F=-1),F}o(_,"scanHexDigits");function d(c){n=c,l="",i=0,u=16,b=0}o(d,"setPosition");function A(){let c=n;if(e.charCodeAt(n)===48)n++;else for(n++;n<e.length&&U(e.charCodeAt(n));)n++;if(n<e.length&&e.charCodeAt(n)===46)if(n++,n<e.length&&U(e.charCodeAt(n)))for(n++;n<e.length&&U(e.charCodeAt(n));)n++;else return b=3,e.substring(c,n);let k=n;if(n<e.length&&(e.charCodeAt(n)===69||e.charCodeAt(n)===101))if(n++,(n<e.length&&e.charCodeAt(n)===43||e.charCodeAt(n)===45)&&n++,n<e.length&&U(e.charCodeAt(n))){for(n++;n<e.length&&U(e.charCodeAt(n));)n++;k=n}else b=3;return e.substring(c,k)}o(A,"scanNumber");function w(){let c="",k=n;for(;;){if(n>=s){c+=e.substring(k,n),b=2;break}const p=e.charCodeAt(n);if(p===34){c+=e.substring(k,n),n++;break}if(p===92){if(c+=e.substring(k,n),n++,n>=s){b=2;break}switch(e.charCodeAt(n++)){case 34:c+='"';break;case 92:c+="\\";break;case 47:c+="/";break;case 98:c+="\b";break;case 102:c+="\f";break;case 110:c+=`
`;break;case 114:c+="\r";break;case 116:c+="	";break;case 117:const j=_(4);j>=0?c+=String.fromCharCode(j):b=4;break;default:b=5}k=n;continue}if(p>=0&&p<=31)if(x(p)){c+=e.substring(k,n),b=2;break}else b=6;n++}return c}o(w,"scanString");function y(){if(l="",b=0,i=n,r=f,T=g,n>=s)return i=s,u=17;let c=e.charCodeAt(n);if(Q(c)){do n++,l+=String.fromCharCode(c),c=e.charCodeAt(n);while(Q(c));return u=15}if(x(c))return n++,l+=String.fromCharCode(c),c===13&&e.charCodeAt(n)===10&&(n++,l+=`
`),f++,g=n,u=14;switch(c){case 123:return n++,u=1;case 125:return n++,u=2;case 91:return n++,u=3;case 93:return n++,u=4;case 58:return n++,u=6;case 44:return n++,u=5;case 34:return n++,l=w(),u=10;case 47:const k=n-1;if(e.charCodeAt(n+1)===47){for(n+=2;n<s&&!x(e.charCodeAt(n));)n++;return l=e.substring(k,n),u=12}if(e.charCodeAt(n+1)===42){n+=2;const p=s-1;let F=!1;for(;n<p;){const j=e.charCodeAt(n);if(j===42&&e.charCodeAt(n+1)===47){n+=2,F=!0;break}n++,x(j)&&(j===13&&e.charCodeAt(n)===10&&n++,f++,g=n)}return F||(n++,b=1),l=e.substring(k,n),u=13}return l+=String.fromCharCode(c),n++,u=16;case 45:if(l+=String.fromCharCode(c),n++,n===s||!U(e.charCodeAt(n)))return u=16;case 48:case 49:case 50:case 51:case 52:case 53:case 54:case 55:case 56:case 57:return l+=A(),u=11;default:for(;n<s&&I(c);)n++,c=e.charCodeAt(n);if(i!==n){switch(l=e.substring(i,n),l){case"true":return u=8;case"false":return u=9;case"null":return u=7}return u=16}return l+=String.fromCharCode(c),n++,u=16}}o(y,"scanNext");function I(c){if(Q(c)||x(c))return!1;switch(c){case 125:case 93:case 123:case 91:case 34:case 58:case 44:case 47:return!1}return!0}o(I,"isUnknownContentCharacter");function L(){let c;do c=y();while(c>=12&&c<=15);return c}return o(L,"scanNextNonTrivia"),{setPosition:d,getPosition:o(()=>n,"getPosition"),scan:t?L:y,getToken:o(()=>u,"getToken"),getTokenValue:o(()=>l,"getTokenValue"),getTokenOffset:o(()=>i,"getTokenOffset"),getTokenLength:o(()=>n-i,"getTokenLength"),getTokenStartLine:o(()=>r,"getTokenStartLine"),getTokenStartCharacter:o(()=>i-T,"getTokenStartCharacter"),getTokenError:o(()=>b,"getTokenError")}}o(je,"createScanner");function Q(e){return e===32||e===9}o(Q,"isWhiteSpace");function x(e){return e===10||e===13}o(x,"isLineBreak");function U(e){return e>=48&&e<=57}o(U,"isDigit");var le;(function(e){e[e.lineFeed=10]="lineFeed",e[e.carriageReturn=13]="carriageReturn",e[e.space=32]="space",e[e._0=48]="_0",e[e._1=49]="_1",e[e._2=50]="_2",e[e._3=51]="_3",e[e._4=52]="_4",e[e._5=53]="_5",e[e._6=54]="_6",e[e._7=55]="_7",e[e._8=56]="_8",e[e._9=57]="_9",e[e.a=97]="a",e[e.b=98]="b",e[e.c=99]="c",e[e.d=100]="d",e[e.e=101]="e",e[e.f=102]="f",e[e.g=103]="g",e[e.h=104]="h",e[e.i=105]="i",e[e.j=106]="j",e[e.k=107]="k",e[e.l=108]="l",e[e.m=109]="m",e[e.n=110]="n",e[e.o=111]="o",e[e.p=112]="p",e[e.q=113]="q",e[e.r=114]="r",e[e.s=115]="s",e[e.t=116]="t",e[e.u=117]="u",e[e.v=118]="v",e[e.w=119]="w",e[e.x=120]="x",e[e.y=121]="y",e[e.z=122]="z",e[e.A=65]="A",e[e.B=66]="B",e[e.C=67]="C",e[e.D=68]="D",e[e.E=69]="E",e[e.F=70]="F",e[e.G=71]="G",e[e.H=72]="H",e[e.I=73]="I",e[e.J=74]="J",e[e.K=75]="K",e[e.L=76]="L",e[e.M=77]="M",e[e.N=78]="N",e[e.O=79]="O",e[e.P=80]="P",e[e.Q=81]="Q",e[e.R=82]="R",e[e.S=83]="S",e[e.T=84]="T",e[e.U=85]="U",e[e.V=86]="V",e[e.W=87]="W",e[e.X=88]="X",e[e.Y=89]="Y",e[e.Z=90]="Z",e[e.asterisk=42]="asterisk",e[e.backslash=92]="backslash",e[e.closeBrace=125]="closeBrace",e[e.closeBracket=93]="closeBracket",e[e.colon=58]="colon",e[e.comma=44]="comma",e[e.dot=46]="dot",e[e.doubleQuote=34]="doubleQuote",e[e.minus=45]="minus",e[e.openBrace=123]="openBrace",e[e.openBracket=91]="openBracket",e[e.plus=43]="plus",e[e.slash=47]="slash",e[e.formFeed=12]="formFeed",e[e.tab=9]="tab"})(le||(le={})),new Array(20).fill(0).map((e,t)=>" ".repeat(t));const h=200;new Array(h).fill(0).map((e,t)=>`
`+" ".repeat(t)),new Array(h).fill(0).map((e,t)=>"\r"+" ".repeat(t)),new Array(h).fill(0).map((e,t)=>`\r
`+" ".repeat(t)),new Array(h).fill(0).map((e,t)=>`
`+"	".repeat(t)),new Array(h).fill(0).map((e,t)=>"\r"+"	".repeat(t)),new Array(h).fill(0).map((e,t)=>`\r
`+"	".repeat(t));var R;(function(e){e.DEFAULT={allowTrailingComma:!1}})(R||(R={}));function ye(e,t=[],s=R.DEFAULT){let n=null,l=[];const i=[];function u(r){Array.isArray(l)?l.push(r):n!==null&&(l[n]=r)}return o(u,"onValue"),Fe(e,{onObjectBegin:o(()=>{const r={};u(r),i.push(l),l=r,n=null},"onObjectBegin"),onObjectProperty:o(r=>{n=r},"onObjectProperty"),onObjectEnd:o(()=>{l=i.pop()},"onObjectEnd"),onArrayBegin:o(()=>{const r=[];u(r),i.push(l),l=r,n=null},"onArrayBegin"),onArrayEnd:o(()=>{l=i.pop()},"onArrayEnd"),onLiteralValue:u,onError:o((r,g,T)=>{t.push({error:r,offset:g,length:T})},"onError")},s),l[0]}o(ye,"parse$1");function Fe(e,t,s=R.DEFAULT){const n=je(e,!1),l=[];function i(v){return v?()=>v(n.getTokenOffset(),n.getTokenLength(),n.getTokenStartLine(),n.getTokenStartCharacter()):()=>!0}o(i,"toNoArgVisit");function u(v){return v?()=>v(n.getTokenOffset(),n.getTokenLength(),n.getTokenStartLine(),n.getTokenStartCharacter(),()=>l.slice()):()=>!0}o(u,"toNoArgVisitWithPath");function f(v){return v?D=>v(D,n.getTokenOffset(),n.getTokenLength(),n.getTokenStartLine(),n.getTokenStartCharacter()):()=>!0}o(f,"toOneArgVisit");function r(v){return v?D=>v(D,n.getTokenOffset(),n.getTokenLength(),n.getTokenStartLine(),n.getTokenStartCharacter(),()=>l.slice()):()=>!0}o(r,"toOneArgVisitWithPath");const g=u(t.onObjectBegin),T=r(t.onObjectProperty),b=i(t.onObjectEnd),_=u(t.onArrayBegin),d=i(t.onArrayEnd),A=r(t.onLiteralValue),w=f(t.onSeparator),y=i(t.onComment),I=f(t.onError),L=s&&s.disallowComments,c=s&&s.allowTrailingComma;function k(){for(;;){const v=n.scan();switch(n.getTokenError()){case 4:p(14);break;case 5:p(15);break;case 3:p(13);break;case 1:L||p(11);break;case 2:p(12);break;case 6:p(16);break}switch(v){case 12:case 13:L?p(10):y();break;case 16:p(1);break;case 15:case 14:break;default:return v}}}o(k,"scanNext");function p(v,D=[],ne=[]){if(I(v),D.length+ne.length>0){let S=n.getToken();for(;S!==17;){if(D.indexOf(S)!==-1){k();break}else if(ne.indexOf(S)!==-1)break;S=k()}}}o(p,"handleError");function F(v){const D=n.getTokenValue();return v?A(D):(T(D),l.push(D)),k(),!0}o(F,"parseString");function j(){switch(n.getToken()){case 11:const v=n.getTokenValue();let D=Number(v);isNaN(D)&&(p(2),D=0),A(D);break;case 7:A(null);break;case 8:A(!0);break;case 9:A(!1);break;default:return!1}return k(),!0}o(j,"parseLiteral");function a(){return n.getToken()!==10?(p(3,[],[2,5]),!1):(F(!1),n.getToken()===6?(w(":"),k(),J()||p(4,[],[2,5])):p(5,[],[2,5]),l.pop(),!0)}o(a,"parseProperty");function N(){g(),k();let v=!1;for(;n.getToken()!==2&&n.getToken()!==17;){if(n.getToken()===5){if(v||p(4,[],[]),w(","),k(),n.getToken()===2&&c)break}else v&&p(6,[],[]);a()||p(4,[],[2,5]),v=!0}return b(),n.getToken()!==2?p(7,[2],[]):k(),!0}o(N,"parseObject");function be(){_(),k();let v=!0,D=!1;for(;n.getToken()!==4&&n.getToken()!==17;){if(n.getToken()===5){if(D||p(4,[],[]),w(","),k(),n.getToken()===4&&c)break}else D&&p(6,[],[]);v?(l.push(0),v=!1):l[l.length-1]++,J()||p(4,[],[4,5]),D=!0}return d(),v||l.pop(),n.getToken()!==4?p(8,[4],[]):k(),!0}o(be,"parseArray");function J(){switch(n.getToken()){case 3:return be();case 1:return N();case 10:return F(!0);default:return j()}}return o(J,"parseValue"),k(),n.getToken()===17?s.allowEmptyContent?!0:(p(4,[],[]),!1):J()?(n.getToken()!==17&&p(9,[],[]),!0):(p(4,[],[]),!1)}o(Fe,"visit");var ie;(function(e){e[e.None=0]="None",e[e.UnexpectedEndOfComment=1]="UnexpectedEndOfComment",e[e.UnexpectedEndOfString=2]="UnexpectedEndOfString",e[e.UnexpectedEndOfNumber=3]="UnexpectedEndOfNumber",e[e.InvalidUnicode=4]="InvalidUnicode",e[e.InvalidEscapeCharacter=5]="InvalidEscapeCharacter",e[e.InvalidCharacter=6]="InvalidCharacter"})(ie||(ie={}));var oe;(function(e){e[e.OpenBraceToken=1]="OpenBraceToken",e[e.CloseBraceToken=2]="CloseBraceToken",e[e.OpenBracketToken=3]="OpenBracketToken",e[e.CloseBracketToken=4]="CloseBracketToken",e[e.CommaToken=5]="CommaToken",e[e.ColonToken=6]="ColonToken",e[e.NullKeyword=7]="NullKeyword",e[e.TrueKeyword=8]="TrueKeyword",e[e.FalseKeyword=9]="FalseKeyword",e[e.StringLiteral=10]="StringLiteral",e[e.NumericLiteral=11]="NumericLiteral",e[e.LineCommentTrivia=12]="LineCommentTrivia",e[e.BlockCommentTrivia=13]="BlockCommentTrivia",e[e.LineBreakTrivia=14]="LineBreakTrivia",e[e.Trivia=15]="Trivia",e[e.Unknown=16]="Unknown",e[e.EOF=17]="EOF"})(oe||(oe={}));const De=ye;var ue;(function(e){e[e.InvalidSymbol=1]="InvalidSymbol",e[e.InvalidNumberFormat=2]="InvalidNumberFormat",e[e.PropertyNameExpected=3]="PropertyNameExpected",e[e.ValueExpected=4]="ValueExpected",e[e.ColonExpected=5]="ColonExpected",e[e.CommaExpected=6]="CommaExpected",e[e.CloseBraceExpected=7]="CloseBraceExpected",e[e.CloseBracketExpected=8]="CloseBracketExpected",e[e.EndOfFileExpected=9]="EndOfFileExpected",e[e.InvalidCommentToken=10]="InvalidCommentToken",e[e.UnexpectedEndOfComment=11]="UnexpectedEndOfComment",e[e.UnexpectedEndOfString=12]="UnexpectedEndOfString",e[e.UnexpectedEndOfNumber=13]="UnexpectedEndOfNumber",e[e.InvalidUnicode=14]="InvalidUnicode",e[e.InvalidEscapeCharacter=15]="InvalidEscapeCharacter",e[e.InvalidCharacter=16]="InvalidCharacter"})(ue||(ue={}));const re=o((e,t)=>De(_e(t,e,"utf8")),"readJsonc"),H=Symbol("implicitBaseUrl"),$="${configDir}",Ee=o(()=>{const{findPnpApi:e}=ve;return e&&e(process.cwd())},"getPnpApi"),X=o((e,t,s,n)=>{const l=`resolveFromPackageJsonPath:${e}:${t}:${s}`;if(n!=null&&n.has(l))return n.get(l);const i=re(e,n);if(!i)return;let u=t||"tsconfig.json";if(!s&&i.exports)try{const[f]=Te(i.exports,t,["require","types"]);u=f}catch{return!1}else!t&&i.tsconfig&&(u=i.tsconfig);return u=m.join(e,"..",u),n==null||n.set(l,u),u},"resolveFromPackageJsonPath"),Y="package.json",Z="tsconfig.json",Be=o((e,t,s)=>{let n=e;if(e===".."&&(n=m.join(n,Z)),e[0]==="."&&(n=m.resolve(t,n)),m.isAbsolute(n)){if(B(s,n)){if(P(s,n).isFile())return n}else if(!n.endsWith(".json")){const d=`${n}.json`;if(B(s,d))return d}return}const[l,...i]=e.split("/"),u=l[0]==="@"?`${l}/${i.shift()}`:l,f=i.join("/"),r=Ee();if(r){const{resolveRequest:d}=r;try{if(u===e){const A=d(m.join(u,Y),t);if(A){const w=X(A,f,!1,s);if(w&&B(s,w))return w}}else{let A;try{A=d(e,t,{extensions:[".json"]})}catch{A=d(m.join(e,Z),t)}if(A)return A}}catch{}}const g=se(m.resolve(t),m.join("node_modules",u),s);if(!g||!P(s,g).isDirectory())return;const T=m.join(g,Y);if(B(s,T)){const d=X(T,f,!1,s);if(d===!1)return;if(d&&B(s,d)&&P(s,d).isFile())return d}const b=m.join(g,f),_=b.endsWith(".json");if(!_){const d=`${b}.json`;if(B(s,d))return d}if(B(s,b)){if(P(s,b).isDirectory()){const d=m.join(b,Y);if(B(s,d)){const w=X(d,"",!0,s);if(w&&B(s,w))return w}const A=m.join(b,Z);if(B(s,A))return A}else if(_)return b}},"resolveExtendsPath"),q=o((e,t)=>G(m.relative(e,t)),"pathRelative"),fe=["files","include","exclude"],Ie=o((e,t,s,n)=>{const l=Be(e,t,n);if(!l)throw new Error(`File '${e}' not found.`);if(s.has(l))throw new Error(`Circularity detected while resolving configuration: ${l}`);s.add(l);const i=m.dirname(l),u=ce(l,n,s);delete u.references;const{compilerOptions:f}=u;if(f){const{baseUrl:r}=f;r&&!r.startsWith($)&&(f.baseUrl=E(m.relative(t,m.join(i,r)))||"./");let{outDir:g}=f;g&&(g.startsWith($)||(g=m.relative(t,m.join(i,g))),f.outDir=E(g)||"./")}for(const r of fe){const g=u[r];g&&(u[r]=g.map(T=>T.startsWith($)?T:E(m.relative(t,m.join(i,T)))))}return u},"resolveExtends"),Le=["outDir","declarationDir"],ce=o((e,t,s=new Set)=>{let n;try{n=re(e,t)||{}}catch{throw new Error(`Cannot resolve tsconfig at path: ${e}`)}if(typeof n!="object")throw new SyntaxError(`Failed to parse tsconfig at: ${e}`);const l=m.dirname(e);if(n.compilerOptions){const{compilerOptions:i}=n;i.paths&&!i.baseUrl&&(i[H]=l)}if(n.extends){const i=Array.isArray(n.extends)?n.extends:[n.extends];delete n.extends;for(const u of i.reverse()){const f=Ie(u,l,new Set(s),t),r={...f,...n,compilerOptions:{...f.compilerOptions,...n.compilerOptions}};f.watchOptions&&(r.watchOptions={...f.watchOptions,...n.watchOptions}),n=r}}if(n.compilerOptions){const{compilerOptions:i}=n,u=["baseUrl","rootDir"];for(const f of u){const r=i[f];if(r&&!r.startsWith($)){const g=m.resolve(l,r),T=q(l,g);i[f]=T}}for(const f of Le){let r=i[f];r&&(Array.isArray(n.exclude)||(n.exclude=[]),n.exclude.includes(r)||n.exclude.push(r),r.startsWith($)||(r=G(r)),i[f]=r)}}else n.compilerOptions={};if(n.include?(n.include=n.include.map(E),n.files&&delete n.files):n.files&&(n.files=n.files.map(i=>i.startsWith($)?i:G(i))),n.watchOptions){const{watchOptions:i}=n;i.excludeDirectories&&(i.excludeDirectories=i.excludeDirectories.map(u=>E(m.resolve(l,u))))}return n},"_parseTsconfig"),W=o((e,t)=>{if(e.startsWith($))return E(m.join(t,e.slice($.length)))},"interpolateConfigDir"),$e=["outDir","declarationDir","outFile","rootDir","baseUrl","tsBuildInfoFile"],Ue=o(e=>{var t,s,n,l,i,u,f,r,g,T,b,_,d,A,w,y,I,L,c,k,p,F,j;if(e.strict){const a=["noImplicitAny","noImplicitThis","strictNullChecks","strictFunctionTypes","strictBindCallApply","strictPropertyInitialization","strictBuiltinIteratorReturn","alwaysStrict","useUnknownInCatchVariables"];for(const N of a)e[N]===void 0&&(e[N]=!0)}if(e.target){let a=e.target.toLowerCase();a==="es2015"&&(a="es6"),e.target=a,a==="esnext"&&((t=e.module)!=null||(e.module="es6"),(s=e.useDefineForClassFields)!=null||(e.useDefineForClassFields=!0)),(a==="es6"||a==="es2016"||a==="es2017"||a==="es2018"||a==="es2019"||a==="es2020"||a==="es2021"||a==="es2022"||a==="es2023"||a==="es2024")&&((n=e.module)!=null||(e.module="es6")),(a==="es2022"||a==="es2023"||a==="es2024")&&((l=e.useDefineForClassFields)!=null||(e.useDefineForClassFields=!0))}if(e.module){let a=e.module.toLowerCase();a==="es2015"&&(a="es6"),e.module=a,(a==="es6"||a==="es2020"||a==="es2022"||a==="esnext"||a==="none"||a==="system"||a==="umd"||a==="amd")&&((i=e.moduleResolution)!=null||(e.moduleResolution="classic")),a==="system"&&((u=e.allowSyntheticDefaultImports)!=null||(e.allowSyntheticDefaultImports=!0)),(a==="node16"||a==="nodenext"||a==="preserve")&&((f=e.esModuleInterop)!=null||(e.esModuleInterop=!0),(r=e.allowSyntheticDefaultImports)!=null||(e.allowSyntheticDefaultImports=!0)),(a==="node16"||a==="nodenext")&&((g=e.moduleDetection)!=null||(e.moduleDetection="force"),(T=e.useDefineForClassFields)!=null||(e.useDefineForClassFields=!0)),a==="node16"&&((b=e.target)!=null||(e.target="es2022"),(_=e.moduleResolution)!=null||(e.moduleResolution="node16")),a==="nodenext"&&((d=e.target)!=null||(e.target="esnext"),(A=e.moduleResolution)!=null||(e.moduleResolution="nodenext")),a==="preserve"&&((w=e.moduleResolution)!=null||(e.moduleResolution="bundler"))}if(e.moduleResolution){let a=e.moduleResolution.toLowerCase();a==="node"&&(a="node10"),e.moduleResolution=a,(a==="node16"||a==="nodenext"||a==="bundler")&&((y=e.resolvePackageJsonExports)!=null||(e.resolvePackageJsonExports=!0),(I=e.resolvePackageJsonImports)!=null||(e.resolvePackageJsonImports=!0)),a==="bundler"&&((L=e.allowSyntheticDefaultImports)!=null||(e.allowSyntheticDefaultImports=!0),(c=e.resolveJsonModule)!=null||(e.resolveJsonModule=!0))}e.esModuleInterop&&((k=e.allowSyntheticDefaultImports)!=null||(e.allowSyntheticDefaultImports=!0)),e.verbatimModuleSyntax&&((p=e.isolatedModules)!=null||(e.isolatedModules=!0),(F=e.preserveConstEnums)!=null||(e.preserveConstEnums=!0)),e.isolatedModules&&((j=e.preserveConstEnums)!=null||(e.preserveConstEnums=!0))},"normalizeCompilerOptions"),ae=o((e,t=new Map)=>{const s=m.resolve(e),n=ce(s,t),l=m.dirname(s),{compilerOptions:i}=n;if(i){for(const f of $e){const r=i[f];if(r){const g=W(r,l);i[f]=g?q(l,g):r}}for(const f of["rootDirs","typeRoots"]){const r=i[f];r&&(i[f]=r.map(g=>{const T=W(g,l);return T?q(l,T):g}))}const{paths:u}=i;if(u)for(const f of Object.keys(u))u[f]=u[f].map(r=>{var g;return(g=W(r,l))!=null?g:r});Ue(i)}for(const u of fe){const f=n[u];f&&(n[u]=f.map(r=>{var g;return(g=W(r,l))!=null?g:r}))}return n},"parseTsconfig"),he=o((e=process.cwd(),t="tsconfig.json",s=new Map)=>{const n=se(E(e),t,s);if(!n)return null;const l=ae(n,s);return{path:n,config:l}},"getTsconfig"),xe=/\*/g,ge=o((e,t)=>{const s=e.match(xe);if(s&&s.length>1)throw new Error(t)},"assertStarCount"),Ne=o(e=>{if(e.includes("*")){const[t,s]=e.split("*");return{prefix:t,suffix:s}}return e},"parsePattern"),Se=o(({prefix:e,suffix:t},s)=>s.startsWith(e)&&s.endsWith(t),"isPatternMatch"),Pe=o((e,t,s)=>Object.entries(e).map(([n,l])=>(ge(n,`Pattern '${n}' can have at most one '*' character.`),{pattern:Ne(n),substitutions:l.map(i=>{if(ge(i,`Substitution '${i}' in pattern '${n}' can have at most one '*' character.`),!t&&!z.test(i))throw new Error("Non-relative paths are not allowed when 'baseUrl' is not set. Did you forget a leading './'?");return m.resolve(s,i)})})),"parsePaths"),Re=o(e=>{const{compilerOptions:t}=e.config;if(!t)return null;const{baseUrl:s,paths:n}=t;if(!s&&!n)return null;const l=H in t&&t[H],i=m.resolve(m.dirname(e.path),s||l||"."),u=n?Pe(n,s,i):[];return f=>{if(z.test(f))return[];const r=[];for(const _ of u){if(_.pattern===f)return _.substitutions.map(E);typeof _.pattern!="string"&&r.push(_)}let g,T=-1;for(const _ of r)Se(_.pattern,f)&&_.pattern.prefix.length>T&&(T=_.pattern.prefix.length,g=_);if(!g)return s?[E(m.join(i,f))]:[];const b=f.slice(g.pattern.prefix.length,f.length-g.pattern.suffix.length);return g.substitutions.map(_=>E(_.replace("*",b)))}},"createPathsMatcher"),pe=o(e=>{let t="";for(let s=0;s<e.length;s+=1){const n=e[s],l=n.toUpperCase();t+=n===l?n.toLowerCase():l}return t},"s"),We=65,Me=97,Ve=o(()=>Math.floor(Math.random()*26),"m"),Je=o(e=>Array.from({length:e},()=>String.fromCodePoint(Ve()+(Math.random()>.5?We:Me))).join(""),"S"),Oe=o((e=Ae)=>{const t=process.execPath;if(e.existsSync(t))return!e.existsSync(pe(t));const s=`/${Je(10)}`;e.writeFileSync(s,"");const n=!e.existsSync(pe(s));return e.unlinkSync(s),n},"l"),{join:M}=m.posix,K={ts:[".ts",".tsx",".d.ts"],cts:[".cts",".d.cts"],mts:[".mts",".d.mts"]},ze=o(e=>{const t=[...K.ts],s=[...K.cts],n=[...K.mts];return e!=null&&e.allowJs&&(t.push(".js",".jsx"),s.push(".cjs"),n.push(".mjs")),[...t,...s,...n]},"getSupportedExtensions"),Ge=o(e=>{const t=[];if(!e)return t;const{outDir:s,declarationDir:n}=e;return s&&t.push(s),n&&t.push(n),t},"getDefaultExcludeSpec"),me=o(e=>e.replaceAll(/[.*+?^${}()|[\]\\]/g,String.raw`\$&`),"escapeForRegexp"),Qe=["node_modules","bower_components","jspm_packages"],C=`(?!(${Qe.join("|")})(/|$))`,He=/(?:^|\/)[^.*?]+$/,ke="**/*",V="[^/]",ee="[^./]",we=process.platform==="win32",Xe=o(({config:e,path:t},s=Oe())=>{if("extends"in e)throw new Error("tsconfig#extends must be resolved. Use getTsconfig or parseTsconfig to resolve it.");if(!m.isAbsolute(t))throw new Error("The tsconfig path must be absolute");we&&(t=E(t));const n=m.dirname(t),{files:l,include:i,exclude:u,compilerOptions:f}=e,r=l==null?void 0:l.map(w=>M(n,w)),g=ze(f),T=s?"":"i",_=(u||Ge(f)).map(w=>{const y=M(n,w),I=me(y).replaceAll(String.raw`\*\*/`,"(.+/)?").replaceAll(String.raw`\*`,`${V}*`).replaceAll(String.raw`\?`,V);return new RegExp(`^${I}($|/)`,T)}),d=l||i?i:[ke],A=d?d.map(w=>{let y=M(n,w);He.test(y)&&(y=M(y,ke));const I=me(y).replaceAll(String.raw`/\*\*`,`(/${C}${ee}${V}*)*?`).replaceAll(/(\/)?\\\*/g,(L,c)=>{const k=`(${ee}|(\\.(?!min\\.js$))?)*`;return c?`/${C}${ee}${k}`:k}).replaceAll(/(\/)?\\\?/g,(L,c)=>{const k=V;return c?`/${C}${k}`:k});return new RegExp(`^${I}$`,T)}):void 0;return w=>{if(!m.isAbsolute(w))throw new Error("filePath must be absolute");if(we&&(w=E(w)),r!=null&&r.includes(w))return e;if(!(!g.some(y=>w.endsWith(y))||_.some(y=>y.test(w)))&&A&&A.some(y=>y.test(w)))return e}},"createFilesMatcher");export{Xe as createFilesMatcher,Re as createPathsMatcher,he as getTsconfig,ae as parseTsconfig};
