{"name": "eslint-plugin-react", "version": "7.37.5", "author": "<PERSON><PERSON> <<EMAIL>>", "description": "React specific linting rules for ESLint", "main": "index.js", "types": "index.d.ts", "scripts": {"clean-built-types": "rm -f $(find . -maxdepth 1 -type f -name '*.d.ts*') $(find lib -type f -name '*.d.ts*' ! -name 'types.d.ts')", "prebuild-types": "npm run clean-built-types", "build-types": "tsc -p build.tsconfig.json", "prepack": "npm run build-types && npmignore --auto --commentLines=autogenerated", "prelint": "npm run lint:docs", "lint:docs": "markdownlint \"**/*.md\"", "postlint:docs": "npm run update:eslint-docs -- --check", "lint": "eslint .", "postlint": "npm run type-check", "pretest": "npm run lint", "test": "npm run unit-test", "posttest": "npx npm@'>= 10.2' audit --production", "type-check": "tsc", "unit-test": "istanbul cover node_modules/mocha/bin/_mocha tests/lib/**/*.js tests/util/**/*.js tests/index.js tests/flat-config.js", "update:eslint-docs": "eslint-doc-generator"}, "repository": {"type": "git", "url": "https://github.com/jsx-eslint/eslint-plugin-react"}, "directories": {"test": ["test", "tests", "test-published-types"]}, "homepage": "https://github.com/jsx-eslint/eslint-plugin-react", "bugs": "https://github.com/jsx-eslint/eslint-plugin-react/issues", "dependencies": {"array-includes": "^3.1.8", "array.prototype.findlast": "^1.2.5", "array.prototype.flatmap": "^1.3.3", "array.prototype.tosorted": "^1.1.4", "doctrine": "^2.1.0", "es-iterator-helpers": "^1.2.1", "estraverse": "^5.3.0", "hasown": "^2.0.2", "jsx-ast-utils": "^2.4.1 || ^3.0.0", "minimatch": "^3.1.2", "object.entries": "^1.1.9", "object.fromentries": "^2.0.8", "object.values": "^1.2.1", "prop-types": "^15.8.1", "resolve": "^2.0.0-next.5", "semver": "^6.3.1", "string.prototype.matchall": "^4.0.12", "string.prototype.repeat": "^1.0.0"}, "devDependencies": {"@babel/core": "^7.26.10", "@babel/eslint-parser": "^7.27.0", "@babel/plugin-syntax-decorators": "^7.25.9", "@babel/plugin-syntax-do-expressions": "^7.25.9", "@babel/plugin-syntax-function-bind": "^7.25.9", "@babel/preset-react": "^7.26.3", "@types/eslint": "=7.2.10", "@types/estree": "0.0.52", "@types/node": "^4.9.5", "@typescript-eslint/parser": "^2.34.0 || ^3.10.1 || ^4 || ^5 || ^6.20 || ^7.14.1 || 8.4 - 8.17", "babel-eslint": "^8 || ^9 || ^10.1.0", "eslint": "^3 || ^4 || ^5 || ^6 || ^7 || ^8 || ^9.7", "eslint-config-airbnb-base": "^15.0.0", "eslint-doc-generator": "^1.7.1", "eslint-plugin-eslint-plugin": "^2.3.0 || ^3.5.3 || ^4.0.1 || ^5.0.5", "eslint-plugin-import": "^2.31.0", "eslint-remote-tester": "^3.0.1", "eslint-remote-tester-repositories": "^1.0.1", "eslint-scope": "^3.7.3", "espree": "^3.5.4", "gfm-footnotes": "^1.0.1", "glob": "=10.3.7", "istanbul": "^0.4.5", "jackspeak": "=2.1.1", "ls-engines": "^0.8.1", "markdownlint-cli": "^0.8.0 || ^0.32.2", "mocha": "^5.2.0", "npmignore": "^0.3.1", "sinon": "^7.5.0", "typescript": "^3.9.9", "typescript-eslint-parser": "^20.1.1"}, "peerDependencies": {"eslint": "^3 || ^4 || ^5 || ^6 || ^7 || ^8 || ^9.7"}, "engines": {"node": ">=4"}, "keywords": ["eslint", "eslint-plugin", "eslintplugin", "react"], "license": "MIT", "publishConfig": {"ignore": [".github/", "!lib", "docs/", "test/", "test-published-types/", "tests/", "*.md", "*.config.js", ".eslint-doc-generatorrc.js", ".eslintrc", ".editorconfig", "tsconfig.json", "build.tsconfig.json", ".markdownlint*", "types", "!*.d.ts", "!*.d.ts.map"]}}