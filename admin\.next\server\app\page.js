/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tty");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "(rsc)/../node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.js&appDir=E%3A%5Cd1%5Cadmin%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=E%3A%5Cd1%5Cadmin&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.js&appDir=E%3A%5Cd1%5Cadmin%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=E%3A%5Cd1%5Cadmin&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/../node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?41d8\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/../node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/../node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/../node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.js */ \"(rsc)/./src/app/page.js\")), \"E:\\\\d1\\\\admin\\\\src\\\\app\\\\page.js\"],\n          \n        }]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.js */ \"(rsc)/./src/app/layout.js\")), \"E:\\\\d1\\\\admin\\\\src\\\\app\\\\layout.js\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/../node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"E:\\\\d1\\\\admin\\\\src\\\\app\\\\page.js\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.js&appDir=E%3A%5Cd1%5Cadmin%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=E%3A%5Cd1%5Cadmin&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5Cd1%5C%5Cadmin%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cd1%5C%5Cadmin%5C%5Csrc%5C%5Ccomponents%5C%5CAdminLayout.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cd1%5C%5Cadmin%5C%5Csrc%5C%5Ccontext%5C%5CAuthContext.js%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cd1%5C%5Cnode_modules%5C%5Creact-toastify%5C%5Cdist%5C%5Creact-toastify.esm.mjs%22%2C%22ids%22%3A%5B%22ToastContainer%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cd1%5C%5Cnode_modules%5C%5Creact-toastify%5C%5Cdist%5C%5CReactToastify.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5Cd1%5C%5Cadmin%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cd1%5C%5Cadmin%5C%5Csrc%5C%5Ccomponents%5C%5CAdminLayout.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cd1%5C%5Cadmin%5C%5Csrc%5C%5Ccontext%5C%5CAuthContext.js%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cd1%5C%5Cnode_modules%5C%5Creact-toastify%5C%5Cdist%5C%5Creact-toastify.esm.mjs%22%2C%22ids%22%3A%5B%22ToastContainer%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cd1%5C%5Cnode_modules%5C%5Creact-toastify%5C%5Cdist%5C%5CReactToastify.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/AdminLayout.js */ \"(ssr)/./src/components/AdminLayout.js\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/context/AuthContext.js */ \"(ssr)/./src/context/AuthContext.js\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ../node_modules/react-toastify/dist/react-toastify.esm.mjs */ \"(ssr)/../node_modules/react-toastify/dist/react-toastify.esm.mjs\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL25leHQvZGlzdC9idWlsZC93ZWJwYWNrL2xvYWRlcnMvbmV4dC1mbGlnaHQtY2xpZW50LWVudHJ5LWxvYWRlci5qcz9tb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJFJTNBJTVDJTVDZDElNUMlNUNhZG1pbiU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q2dsb2JhbHMuY3NzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkUlM0ElNUMlNUNkMSU1QyU1Q2FkbWluJTVDJTVDc3JjJTVDJTVDY29tcG9uZW50cyU1QyU1Q0FkbWluTGF5b3V0LmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyZGVmYXVsdCUyMiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJFJTNBJTVDJTVDZDElNUMlNUNhZG1pbiU1QyU1Q3NyYyU1QyU1Q2NvbnRleHQlNUMlNUNBdXRoQ29udGV4dC5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMkF1dGhQcm92aWRlciUyMiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJFJTNBJTVDJTVDZDElNUMlNUNub2RlX21vZHVsZXMlNUMlNUNyZWFjdC10b2FzdGlmeSU1QyU1Q2Rpc3QlNUMlNUNyZWFjdC10b2FzdGlmeS5lc20ubWpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyVG9hc3RDb250YWluZXIlMjIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyRSUzQSU1QyU1Q2QxJTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDcmVhY3QtdG9hc3RpZnklNUMlNUNkaXN0JTVDJTVDUmVhY3RUb2FzdGlmeS5jc3MlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLDBLQUFnSDtBQUNoSDtBQUNBLG9LQUFrSDtBQUNsSDtBQUNBLGdPQUEySSIsInNvdXJjZXMiOlsid2VicGFjazovL2FkbWluLz9kMzc5Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiZGVmYXVsdFwiXSAqLyBcIkU6XFxcXGQxXFxcXGFkbWluXFxcXHNyY1xcXFxjb21wb25lbnRzXFxcXEFkbWluTGF5b3V0LmpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJBdXRoUHJvdmlkZXJcIl0gKi8gXCJFOlxcXFxkMVxcXFxhZG1pblxcXFxzcmNcXFxcY29udGV4dFxcXFxBdXRoQ29udGV4dC5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiVG9hc3RDb250YWluZXJcIl0gKi8gXCJFOlxcXFxkMVxcXFxub2RlX21vZHVsZXNcXFxccmVhY3QtdG9hc3RpZnlcXFxcZGlzdFxcXFxyZWFjdC10b2FzdGlmeS5lc20ubWpzXCIpO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5Cd1%5C%5Cadmin%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cd1%5C%5Cadmin%5C%5Csrc%5C%5Ccomponents%5C%5CAdminLayout.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cd1%5C%5Cadmin%5C%5Csrc%5C%5Ccontext%5C%5CAuthContext.js%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cd1%5C%5Cnode_modules%5C%5Creact-toastify%5C%5Cdist%5C%5Creact-toastify.esm.mjs%22%2C%22ids%22%3A%5B%22ToastContainer%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cd1%5C%5Cnode_modules%5C%5Creact-toastify%5C%5Cdist%5C%5CReactToastify.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5Cd1%5C%5Cadmin%5C%5Csrc%5C%5Capp%5C%5Cpage.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***********************************************************************************************************************************************************************************************************************!*\
  !*** ../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5Cd1%5C%5Cadmin%5C%5Csrc%5C%5Capp%5C%5Cpage.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***********************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.js */ \"(ssr)/./src/app/page.js\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL25leHQvZGlzdC9idWlsZC93ZWJwYWNrL2xvYWRlcnMvbmV4dC1mbGlnaHQtY2xpZW50LWVudHJ5LWxvYWRlci5qcz9tb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJFJTNBJTVDJTVDZDElNUMlNUNhZG1pbiU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q3BhZ2UuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLDhJQUFxRSIsInNvdXJjZXMiOlsid2VicGFjazovL2FkbWluLz84ZWFkIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRTpcXFxcZDFcXFxcYWRtaW5cXFxcc3JjXFxcXGFwcFxcXFxwYWdlLmpzXCIpO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5Cd1%5C%5Cadmin%5C%5Csrc%5C%5Capp%5C%5Cpage.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5Cd1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cd1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cd1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cd1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cd1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cd1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5Cd1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cd1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cd1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cd1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cd1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cd1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../node_modules/next/dist/client/components/app-router.js */ \"(ssr)/../node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../node_modules/next/dist/client/components/client-page.js */ \"(ssr)/../node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/../node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/../node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/../node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/../node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5Cd1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cd1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cd1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cd1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cd1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cd1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/page.js":
/*!*************************!*\
  !*** ./src/app/page.js ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AdminHome)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nfunction AdminHome() {\n    // Sample data for dashboard\n    const stats = {\n        totalProducts: 25,\n        totalOrders: 150,\n        totalCustomers: 89,\n        totalRevenue: 45000,\n        recentOrders: [\n            {\n                _id: \"1\",\n                customer: {\n                    name: \"أحمد محمد\"\n                },\n                items: [\n                    {\n                        product: {\n                            name: \"كرسي مكتب\"\n                        }\n                    }\n                ],\n                totalAmount: 850,\n                status: \"delivered\"\n            },\n            {\n                _id: \"2\",\n                customer: {\n                    name: \"فاطمة علي\"\n                },\n                items: [\n                    {\n                        product: {\n                            name: \"طاولة طعام\"\n                        }\n                    }\n                ],\n                totalAmount: 1200,\n                status: \"pending\"\n            },\n            {\n                _id: \"3\",\n                customer: {\n                    name: \"محمد سالم\"\n                },\n                items: [\n                    {\n                        product: {\n                            name: \"سرير مزدوج\"\n                        }\n                    }\n                ],\n                totalAmount: 2500,\n                status: \"processing\"\n            }\n        ],\n        topProducts: [\n            {\n                product: {\n                    name: \"كرسي مكتب مريح\"\n                },\n                totalSold: 45,\n                revenue: 38250\n            },\n            {\n                product: {\n                    name: \"طاولة طعام خشبية\"\n                },\n                totalSold: 20,\n                revenue: 24000\n            }\n        ]\n    };\n    const statCards = [\n        {\n            title: \"إجمالي المنتجات\",\n            value: stats.totalProducts,\n            icon: \"\\uD83D\\uDCE6\",\n            color: \"bg-blue-500\"\n        },\n        {\n            title: \"إجمالي الطلبات\",\n            value: stats.totalOrders,\n            icon: \"\\uD83D\\uDED2\",\n            color: \"bg-green-500\"\n        },\n        {\n            title: \"إجمالي العملاء\",\n            value: stats.totalCustomers,\n            icon: \"\\uD83D\\uDC65\",\n            color: \"bg-purple-500\"\n        },\n        {\n            title: \"إجمالي الإيرادات\",\n            value: `${stats.totalRevenue.toLocaleString()} ر.س`,\n            icon: \"\\uD83D\\uDCB0\",\n            color: \"bg-yellow-500\"\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                className: \"text-3xl font-bold text-gray-800 mb-8\",\n                children: \"لوحة التحكم\"\n            }, void 0, false, {\n                fileName: \"E:\\\\d1\\\\admin\\\\src\\\\app\\\\page.js\",\n                lineNumber: 76,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8\",\n                children: statCards.map((stat, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white p-6 rounded-lg shadow-md\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm font-medium text-gray-600\",\n                                            children: stat.title\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\d1\\\\admin\\\\src\\\\app\\\\page.js\",\n                                            lineNumber: 84,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-2xl font-bold text-gray-900\",\n                                            children: stat.value\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\d1\\\\admin\\\\src\\\\app\\\\page.js\",\n                                            lineNumber: 85,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\d1\\\\admin\\\\src\\\\app\\\\page.js\",\n                                    lineNumber: 83,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: `w-12 h-12 rounded-lg flex items-center justify-center text-white text-xl ${stat.color}`,\n                                    children: stat.icon\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\d1\\\\admin\\\\src\\\\app\\\\page.js\",\n                                    lineNumber: 87,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\d1\\\\admin\\\\src\\\\app\\\\page.js\",\n                            lineNumber: 82,\n                            columnNumber: 13\n                        }, this)\n                    }, `stat-${index}`, false, {\n                        fileName: \"E:\\\\d1\\\\admin\\\\src\\\\app\\\\page.js\",\n                        lineNumber: 81,\n                        columnNumber: 11\n                    }, this))\n            }, void 0, false, {\n                fileName: \"E:\\\\d1\\\\admin\\\\src\\\\app\\\\page.js\",\n                lineNumber: 79,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 lg:grid-cols-2 gap-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-lg shadow-md overflow-hidden\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"px-6 py-4 border-b border-gray-200\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-lg font-semibold\",\n                                    children: \"الطلبات الأخيرة\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\d1\\\\admin\\\\src\\\\app\\\\page.js\",\n                                    lineNumber: 99,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\d1\\\\admin\\\\src\\\\app\\\\page.js\",\n                                lineNumber: 98,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"px-6 py-4\",\n                                children: stats.recentOrders.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: stats.recentOrders.map((order)=>{\n                                        const getStatusStyle = (status)=>{\n                                            switch(status){\n                                                case \"delivered\":\n                                                    return \"bg-green-100 text-green-800\";\n                                                case \"pending\":\n                                                    return \"bg-yellow-100 text-yellow-800\";\n                                                default:\n                                                    return \"bg-blue-100 text-blue-800\";\n                                            }\n                                        };\n                                        const getStatusText = (status)=>{\n                                            switch(status){\n                                                case \"delivered\":\n                                                    return \"تم التسليم\";\n                                                case \"pending\":\n                                                    return \"في الانتظار\";\n                                                default:\n                                                    return \"قيد المعالجة\";\n                                            }\n                                        };\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between p-3 bg-gray-50 rounded-lg\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"font-medium\",\n                                                            children: order.customer?.name\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\d1\\\\admin\\\\src\\\\app\\\\page.js\",\n                                                            lineNumber: 124,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-600\",\n                                                            children: [\n                                                                order.items.length,\n                                                                \" منتج - \",\n                                                                order.totalAmount.toLocaleString(),\n                                                                \" ر.س\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\d1\\\\admin\\\\src\\\\app\\\\page.js\",\n                                                            lineNumber: 125,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\d1\\\\admin\\\\src\\\\app\\\\page.js\",\n                                                    lineNumber: 123,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: `px-2 py-1 text-xs rounded-full ${getStatusStyle(order.status)}`,\n                                                    children: getStatusText(order.status)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\d1\\\\admin\\\\src\\\\app\\\\page.js\",\n                                                    lineNumber: 129,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, order._id, true, {\n                                            fileName: \"E:\\\\d1\\\\admin\\\\src\\\\app\\\\page.js\",\n                                            lineNumber: 122,\n                                            columnNumber: 21\n                                        }, this);\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\d1\\\\admin\\\\src\\\\app\\\\page.js\",\n                                    lineNumber: 103,\n                                    columnNumber: 15\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-500 text-center py-4\",\n                                    children: \"لا توجد طلبات حديثة\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\d1\\\\admin\\\\src\\\\app\\\\page.js\",\n                                    lineNumber: 137,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\d1\\\\admin\\\\src\\\\app\\\\page.js\",\n                                lineNumber: 101,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\d1\\\\admin\\\\src\\\\app\\\\page.js\",\n                        lineNumber: 97,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-lg shadow-md overflow-hidden\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"px-6 py-4 border-b border-gray-200\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-lg font-semibold\",\n                                    children: \"المنتجات الأكثر مبيعاً\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\d1\\\\admin\\\\src\\\\app\\\\page.js\",\n                                    lineNumber: 145,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\d1\\\\admin\\\\src\\\\app\\\\page.js\",\n                                lineNumber: 144,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"px-6 py-4\",\n                                children: stats.topProducts.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: stats.topProducts.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between p-3 bg-gray-50 rounded-lg\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"font-medium\",\n                                                            children: item.product?.name\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\d1\\\\admin\\\\src\\\\app\\\\page.js\",\n                                                            lineNumber: 153,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-600\",\n                                                            children: [\n                                                                item.totalSold,\n                                                                \" قطعة مباعة\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\d1\\\\admin\\\\src\\\\app\\\\page.js\",\n                                                            lineNumber: 154,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\d1\\\\admin\\\\src\\\\app\\\\page.js\",\n                                                    lineNumber: 152,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-right\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"font-bold text-green-600\",\n                                                        children: [\n                                                            item.revenue?.toLocaleString(),\n                                                            \" ر.س\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\d1\\\\admin\\\\src\\\\app\\\\page.js\",\n                                                        lineNumber: 159,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\d1\\\\admin\\\\src\\\\app\\\\page.js\",\n                                                    lineNumber: 158,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, `product-${index}`, true, {\n                                            fileName: \"E:\\\\d1\\\\admin\\\\src\\\\app\\\\page.js\",\n                                            lineNumber: 151,\n                                            columnNumber: 19\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\d1\\\\admin\\\\src\\\\app\\\\page.js\",\n                                    lineNumber: 149,\n                                    columnNumber: 15\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-500 text-center py-4\",\n                                    children: \"لا توجد بيانات مبيعات\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\d1\\\\admin\\\\src\\\\app\\\\page.js\",\n                                    lineNumber: 167,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\d1\\\\admin\\\\src\\\\app\\\\page.js\",\n                                lineNumber: 147,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\d1\\\\admin\\\\src\\\\app\\\\page.js\",\n                        lineNumber: 143,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\d1\\\\admin\\\\src\\\\app\\\\page.js\",\n                lineNumber: 95,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\d1\\\\admin\\\\src\\\\app\\\\page.js\",\n        lineNumber: 75,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/page.js\n");

/***/ }),

/***/ "(ssr)/./src/components/AdminLayout.js":
/*!***************************************!*\
  !*** ./src/components/AdminLayout.js ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AdminLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/../node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(ssr)/../node_modules/next/dist/api/link.js\");\n/* harmony import */ var _context_AuthContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../context/AuthContext */ \"(ssr)/./src/context/AuthContext.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n// Using simple text icons instead of react-icons\n\nfunction AdminLayout({ children }) {\n    const [sidebarOpen, setSidebarOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { user, logout, isAuthenticated, loading } = (0,_context_AuthContext__WEBPACK_IMPORTED_MODULE_4__.useAuth)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!loading && !isAuthenticated) {\n            router.push(\"/login\");\n        }\n    }, [\n        isAuthenticated,\n        loading,\n        router\n    ]);\n    const handleLogout = ()=>{\n        logout();\n        router.push(\"/login\");\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex justify-center items-center min-h-screen\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"spinner border-blue-600\"\n            }, void 0, false, {\n                fileName: \"E:\\\\d1\\\\admin\\\\src\\\\components\\\\AdminLayout.js\",\n                lineNumber: 28,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"E:\\\\d1\\\\admin\\\\src\\\\components\\\\AdminLayout.js\",\n            lineNumber: 27,\n            columnNumber: 7\n        }, this);\n    }\n    if (!isAuthenticated) {\n        return null;\n    }\n    const menuItems = [\n        {\n            href: \"/\",\n            icon: \"\\uD83C\\uDFE0\",\n            label: \"الرئيسية\"\n        },\n        {\n            href: \"/products\",\n            icon: \"\\uD83D\\uDCE6\",\n            label: \"المنتجات\"\n        },\n        {\n            href: \"/orders\",\n            icon: \"\\uD83D\\uDED2\",\n            label: \"الطلبات\"\n        },\n        {\n            href: \"/customers\",\n            icon: \"\\uD83D\\uDC65\",\n            label: \"العملاء\"\n        },\n        {\n            href: \"/analytics\",\n            icon: \"\\uD83D\\uDCCA\",\n            label: \"التقارير\"\n        },\n        {\n            href: \"/settings\",\n            icon: \"⚙️\",\n            label: \"الإعدادات\"\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex h-screen bg-gray-100\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: `sidebar ${sidebarOpen ? \"open\" : \"closed\"}`,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-xl font-bold text-white\",\n                            children: \"لوحة التحكم\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\d1\\\\admin\\\\src\\\\components\\\\AdminLayout.js\",\n                            lineNumber: 51,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\d1\\\\admin\\\\src\\\\components\\\\AdminLayout.js\",\n                        lineNumber: 50,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                        className: \"mt-6\",\n                        children: menuItems.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                href: item.href,\n                                className: \"flex items-center px-6 py-3 text-gray-300 hover:bg-gray-700 hover:text-white transition-colors\",\n                                onClick: ()=>setSidebarOpen(false),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"ml-3 text-xl\",\n                                        children: item.icon\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\d1\\\\admin\\\\src\\\\components\\\\AdminLayout.js\",\n                                        lineNumber: 62,\n                                        columnNumber: 15\n                                    }, this),\n                                    item.label\n                                ]\n                            }, item.href, true, {\n                                fileName: \"E:\\\\d1\\\\admin\\\\src\\\\components\\\\AdminLayout.js\",\n                                lineNumber: 56,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"E:\\\\d1\\\\admin\\\\src\\\\components\\\\AdminLayout.js\",\n                        lineNumber: 54,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute bottom-0 w-full p-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: handleLogout,\n                            className: \"flex items-center w-full px-4 py-2 text-gray-300 hover:bg-gray-700 hover:text-white transition-colors rounded-lg\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"ml-3 text-xl\",\n                                    children: \"\\uD83D\\uDEAA\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\d1\\\\admin\\\\src\\\\components\\\\AdminLayout.js\",\n                                    lineNumber: 73,\n                                    columnNumber: 13\n                                }, this),\n                                \"تسجيل الخروج\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\d1\\\\admin\\\\src\\\\components\\\\AdminLayout.js\",\n                            lineNumber: 69,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\d1\\\\admin\\\\src\\\\components\\\\AdminLayout.js\",\n                        lineNumber: 68,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\d1\\\\admin\\\\src\\\\components\\\\AdminLayout.js\",\n                lineNumber: 49,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 flex flex-col overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                        className: \"bg-white shadow-sm border-b border-gray-200\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between px-6 py-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setSidebarOpen(!sidebarOpen),\n                                            className: \"md:hidden p-2 rounded-lg text-gray-600 hover:bg-gray-100\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-2xl\",\n                                                children: sidebarOpen ? \"✕\" : \"☰\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\d1\\\\admin\\\\src\\\\components\\\\AdminLayout.js\",\n                                                lineNumber: 89,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\d1\\\\admin\\\\src\\\\components\\\\AdminLayout.js\",\n                                            lineNumber: 85,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                            className: \"text-xl font-semibold text-gray-800 mr-4\",\n                                            children: [\n                                                \"مرحباً، \",\n                                                user?.name\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\d1\\\\admin\\\\src\\\\components\\\\AdminLayout.js\",\n                                            lineNumber: 91,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\d1\\\\admin\\\\src\\\\components\\\\AdminLayout.js\",\n                                    lineNumber: 84,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-4 space-x-reverse\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm text-gray-600\",\n                                        children: user?.email\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\d1\\\\admin\\\\src\\\\components\\\\AdminLayout.js\",\n                                        lineNumber: 97,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\d1\\\\admin\\\\src\\\\components\\\\AdminLayout.js\",\n                                    lineNumber: 96,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\d1\\\\admin\\\\src\\\\components\\\\AdminLayout.js\",\n                            lineNumber: 83,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\d1\\\\admin\\\\src\\\\components\\\\AdminLayout.js\",\n                        lineNumber: 82,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"flex-1 overflow-y-auto p-6\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"E:\\\\d1\\\\admin\\\\src\\\\components\\\\AdminLayout.js\",\n                        lineNumber: 103,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\d1\\\\admin\\\\src\\\\components\\\\AdminLayout.js\",\n                lineNumber: 80,\n                columnNumber: 7\n            }, this),\n            sidebarOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black bg-opacity-50 z-30 md:hidden\",\n                onClick: ()=>setSidebarOpen(false)\n            }, void 0, false, {\n                fileName: \"E:\\\\d1\\\\admin\\\\src\\\\components\\\\AdminLayout.js\",\n                lineNumber: 110,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\d1\\\\admin\\\\src\\\\components\\\\AdminLayout.js\",\n        lineNumber: 47,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/AdminLayout.js\n");

/***/ }),

/***/ "(ssr)/./src/context/AuthContext.js":
/*!************************************!*\
  !*** ./src/context/AuthContext.js ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var js_cookie__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! js-cookie */ \"(ssr)/../node_modules/js-cookie/dist/js.cookie.mjs\");\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-toastify */ \"(ssr)/../node_modules/react-toastify/dist/react-toastify.esm.mjs\");\n/* harmony import */ var _utils_api__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../utils/api */ \"(ssr)/./src/utils/api.js\");\n/* __next_internal_client_entry_do_not_use__ useAuth,AuthProvider auto */ \n\n\n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)();\nconst useAuth = ()=>{\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (!context) {\n        throw new Error(\"useAuth must be used within an AuthProvider\");\n    }\n    return context;\n};\nconst AuthProvider = ({ children })=>{\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const token = js_cookie__WEBPACK_IMPORTED_MODULE_2__[\"default\"].get(\"admin_token\");\n        if (token) {\n            // Set token in API headers\n            _utils_api__WEBPACK_IMPORTED_MODULE_4__[\"default\"].defaults.headers.common[\"Authorization\"] = `Bearer ${token}`;\n            // Get user profile\n            _utils_api__WEBPACK_IMPORTED_MODULE_4__[\"default\"].get(\"/users/profile\").then((response)=>{\n                const userData = response.data;\n                if (userData.role === \"admin\") {\n                    setUser(userData);\n                } else {\n                    // Not an admin, remove token\n                    js_cookie__WEBPACK_IMPORTED_MODULE_2__[\"default\"].remove(\"admin_token\");\n                    delete _utils_api__WEBPACK_IMPORTED_MODULE_4__[\"default\"].defaults.headers.common[\"Authorization\"];\n                }\n            }).catch(()=>{\n                // Token is invalid, remove it\n                js_cookie__WEBPACK_IMPORTED_MODULE_2__[\"default\"].remove(\"admin_token\");\n                delete _utils_api__WEBPACK_IMPORTED_MODULE_4__[\"default\"].defaults.headers.common[\"Authorization\"];\n            }).finally(()=>{\n                setLoading(false);\n            });\n        } else {\n            setLoading(false);\n        }\n    }, []);\n    const login = async (email, password)=>{\n        try {\n            const response = await _utils_api__WEBPACK_IMPORTED_MODULE_4__[\"default\"].post(\"/auth/login\", {\n                email,\n                password\n            });\n            const { token, user } = response.data;\n            // Check if user is admin\n            if (user.role !== \"admin\") {\n                react_toastify__WEBPACK_IMPORTED_MODULE_3__.toast.error(\"ليس لديك صلاحيات للوصول إلى لوحة التحكم\");\n                return {\n                    success: false,\n                    message: \"ليس لديك صلاحيات للوصول إلى لوحة التحكم\"\n                };\n            }\n            // Store token in cookie\n            js_cookie__WEBPACK_IMPORTED_MODULE_2__[\"default\"].set(\"admin_token\", token, {\n                expires: 1\n            }); // 1 day\n            // Set token in API headers\n            _utils_api__WEBPACK_IMPORTED_MODULE_4__[\"default\"].defaults.headers.common[\"Authorization\"] = `Bearer ${token}`;\n            setUser(user);\n            react_toastify__WEBPACK_IMPORTED_MODULE_3__.toast.success(\"تم تسجيل الدخول بنجاح\");\n            return {\n                success: true\n            };\n        } catch (error) {\n            const message = error.response?.data?.message || \"حدث خطأ في تسجيل الدخول\";\n            react_toastify__WEBPACK_IMPORTED_MODULE_3__.toast.error(message);\n            return {\n                success: false,\n                message\n            };\n        }\n    };\n    const logout = ()=>{\n        js_cookie__WEBPACK_IMPORTED_MODULE_2__[\"default\"].remove(\"admin_token\");\n        delete _utils_api__WEBPACK_IMPORTED_MODULE_4__[\"default\"].defaults.headers.common[\"Authorization\"];\n        setUser(null);\n        react_toastify__WEBPACK_IMPORTED_MODULE_3__.toast.success(\"تم تسجيل الخروج بنجاح\");\n    };\n    const updateProfile = async (userData)=>{\n        try {\n            const response = await _utils_api__WEBPACK_IMPORTED_MODULE_4__[\"default\"].put(\"/users/profile\", userData);\n            setUser(response.data);\n            react_toastify__WEBPACK_IMPORTED_MODULE_3__.toast.success(\"تم تحديث الملف الشخصي بنجاح\");\n            return {\n                success: true\n            };\n        } catch (error) {\n            const message = error.response?.data?.message || \"حدث خطأ في تحديث الملف الشخصي\";\n            react_toastify__WEBPACK_IMPORTED_MODULE_3__.toast.error(message);\n            return {\n                success: false,\n                message\n            };\n        }\n    };\n    const value = {\n        user,\n        loading,\n        login,\n        logout,\n        updateProfile,\n        isAuthenticated: !!user,\n        isAdmin: user?.role === \"admin\"\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"E:\\\\d1\\\\admin\\\\src\\\\context\\\\AuthContext.js\",\n        lineNumber: 112,\n        columnNumber: 5\n    }, undefined);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/context/AuthContext.js\n");

/***/ }),

/***/ "(ssr)/./src/utils/api.js":
/*!**************************!*\
  !*** ./src/utils/api.js ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! axios */ \"(ssr)/../node_modules/axios/lib/axios.js\");\n\nconst API_BASE_URL = \"http://localhost:5000/api\" || 0;\nconst api = axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].create({\n    baseURL: API_BASE_URL,\n    timeout: 10000,\n    headers: {\n        \"Content-Type\": \"application/json\"\n    }\n});\n// Request interceptor\napi.interceptors.request.use((config)=>{\n    // You can add auth token here if needed\n    return config;\n}, (error)=>{\n    return Promise.reject(error);\n});\n// Response interceptor\napi.interceptors.response.use((response)=>{\n    return response;\n}, (error)=>{\n    // Handle common errors\n    if (error.response?.status === 401) {\n        // Unauthorized - redirect to login\n        if (false) {}\n    }\n    return Promise.reject(error);\n});\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (api);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/utils/api.js\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"8cf3c29e0d29\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYWRtaW4vLi9zcmMvYXBwL2dsb2JhbHMuY3NzP2ZmNDUiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCI4Y2YzYzI5ZTBkMjlcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.js":
/*!***************************!*\
  !*** ./src/app/layout.js ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/../node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-toastify */ \"(rsc)/../node_modules/react-toastify/dist/react-toastify.esm.mjs\");\n/* harmony import */ var react_toastify_dist_ReactToastify_css__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-toastify/dist/ReactToastify.css */ \"(rsc)/../node_modules/react-toastify/dist/ReactToastify.css\");\n/* harmony import */ var _context_AuthContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../context/AuthContext */ \"(rsc)/./src/context/AuthContext.js\");\n/* harmony import */ var _components_AdminLayout__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../components/AdminLayout */ \"(rsc)/./src/components/AdminLayout.js\");\n\n\n\n\n\n\nconst metadata = {\n    title: \"لوحة التحكم - متجر الأثاث\",\n    description: \"لوحة تحكم إدارة متجر الأثاث\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"ar\",\n        dir: \"rtl\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_context_AuthContext__WEBPACK_IMPORTED_MODULE_4__.AuthProvider, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AdminLayout__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"E:\\\\d1\\\\admin\\\\src\\\\app\\\\layout.js\",\n                        lineNumber: 17,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_toastify__WEBPACK_IMPORTED_MODULE_2__.ToastContainer, {\n                        position: \"top-right\",\n                        autoClose: 3000,\n                        hideProgressBar: false,\n                        newestOnTop: false,\n                        closeOnClick: true,\n                        rtl: true,\n                        pauseOnFocusLoss: true,\n                        draggable: true,\n                        pauseOnHover: true\n                    }, void 0, false, {\n                        fileName: \"E:\\\\d1\\\\admin\\\\src\\\\app\\\\layout.js\",\n                        lineNumber: 20,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\d1\\\\admin\\\\src\\\\app\\\\layout.js\",\n                lineNumber: 16,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"E:\\\\d1\\\\admin\\\\src\\\\app\\\\layout.js\",\n            lineNumber: 15,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"E:\\\\d1\\\\admin\\\\src\\\\app\\\\layout.js\",\n        lineNumber: 14,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.js\n");

/***/ }),

/***/ "(rsc)/./src/app/page.js":
/*!*************************!*\
  !*** ./src/app/page.js ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/../node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`E:\d1\admin\src\app\page.js#default`));


/***/ }),

/***/ "(rsc)/./src/components/AdminLayout.js":
/*!***************************************!*\
  !*** ./src/components/AdminLayout.js ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/../node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`E:\d1\admin\src\components\AdminLayout.js#default`));


/***/ }),

/***/ "(rsc)/./src/context/AuthContext.js":
/*!************************************!*\
  !*** ./src/context/AuthContext.js ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AuthProvider: () => (/* binding */ e1),
/* harmony export */   useAuth: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/../node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`E:\d1\admin\src\context\AuthContext.js#useAuth`);

const e1 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`E:\d1\admin\src\context\AuthContext.js#AuthProvider`);


/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/mime-db","vendor-chunks/axios","vendor-chunks/react-toastify","vendor-chunks/follow-redirects","vendor-chunks/debug","vendor-chunks/form-data","vendor-chunks/get-intrinsic","vendor-chunks/asynckit","vendor-chunks/combined-stream","vendor-chunks/mime-types","vendor-chunks/js-cookie","vendor-chunks/proxy-from-env","vendor-chunks/ms","vendor-chunks/has-symbols","vendor-chunks/delayed-stream","vendor-chunks/@swc","vendor-chunks/function-bind","vendor-chunks/es-set-tostringtag","vendor-chunks/get-proto","vendor-chunks/call-bind-apply-helpers","vendor-chunks/dunder-proto","vendor-chunks/math-intrinsics","vendor-chunks/es-errors","vendor-chunks/clsx","vendor-chunks/gopd","vendor-chunks/es-define-property","vendor-chunks/hasown","vendor-chunks/has-tostringtag","vendor-chunks/es-object-atoms"], () => (__webpack_exec__("(rsc)/../node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.js&appDir=E%3A%5Cd1%5Cadmin%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=E%3A%5Cd1%5Cadmin&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();