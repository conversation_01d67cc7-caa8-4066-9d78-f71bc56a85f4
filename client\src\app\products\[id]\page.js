'use client';

import { useState, useEffect } from 'react';
import { useParams } from 'next/navigation';
import Image from 'next/image';
import { FiStar, FiShoppingCart, FiMinus, FiPlus, FiHeart } from 'react-icons/fi';
import { useCart } from '../../../context/CartContext';
import { useAuth } from '../../../context/AuthContext';
import api from '../../../utils/api';
import { toast } from 'react-toastify';

export default function ProductDetailPage() {
  const params = useParams();
  const [product, setProduct] = useState(null);
  const [loading, setLoading] = useState(true);
  const [quantity, setQuantity] = useState(1);
  const [selectedImage, setSelectedImage] = useState(0);
  const [newReview, setNewReview] = useState({ rating: 5, comment: '' });
  const [submittingReview, setSubmittingReview] = useState(false);
  
  const { addToCart } = useCart();
  const { isAuthenticated } = useAuth();

  useEffect(() => {
    if (params.id) {
      fetchProduct();
    }
  }, [params.id]);

  const fetchProduct = async () => {
    try {
      const response = await api.get(`/products/${params.id}`);
      setProduct(response.data);
    } catch (error) {
      console.error('Error fetching product:', error);
      toast.error('حدث خطأ في تحميل المنتج');
    } finally {
      setLoading(false);
    }
  };

  const handleAddToCart = () => {
    if (product) {
      addToCart(product, quantity);
    }
  };

  const handleQuantityChange = (change) => {
    const newQuantity = quantity + change;
    if (newQuantity >= 1 && newQuantity <= product.stock) {
      setQuantity(newQuantity);
    }
  };

  const handleSubmitReview = async (e) => {
    e.preventDefault();
    if (!isAuthenticated) {
      toast.error('يجب تسجيل الدخول لإضافة تقييم');
      return;
    }

    setSubmittingReview(true);
    try {
      await api.post(`/products/${params.id}/reviews`, newReview);
      setNewReview({ rating: 5, comment: '' });
      fetchProduct(); // Refresh product data
      toast.success('تم إضافة التقييم بنجاح');
    } catch (error) {
      const message = error.response?.data?.message || 'حدث خطأ في إضافة التقييم';
      toast.error(message);
    } finally {
      setSubmittingReview(false);
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <div className="spinner border-blue-600"></div>
      </div>
    );
  }

  if (!product) {
    return (
      <div className="container mx-auto px-4 py-8 text-center">
        <h1 className="text-2xl font-bold text-gray-600">المنتج غير موجود</h1>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-12">
        {/* Product Images */}
        <div>
          <div className="mb-4">
            {product.images && product.images[selectedImage] && (
              <Image
                src={product.images[selectedImage]}
                alt={product.name}
                width={600}
                height={600}
                className="w-full h-96 object-cover rounded-lg"
              />
            )}
          </div>
          
          {product.images && product.images.length > 1 && (
            <div className="flex space-x-2 space-x-reverse overflow-x-auto">
              {product.images.map((image, index) => (
                <button
                  key={index}
                  onClick={() => setSelectedImage(index)}
                  className={`flex-shrink-0 w-20 h-20 rounded-lg overflow-hidden border-2 ${
                    selectedImage === index ? 'border-blue-600' : 'border-gray-200'
                  }`}
                >
                  <Image
                    src={image}
                    alt={`${product.name} ${index + 1}`}
                    width={80}
                    height={80}
                    className="w-full h-full object-cover"
                  />
                </button>
              ))}
            </div>
          )}
        </div>

        {/* Product Info */}
        <div>
          <h1 className="text-3xl font-bold mb-4">{product.name}</h1>
          
          <div className="flex items-center mb-4">
            <div className="flex items-center text-yellow-400 mr-2">
              {[...Array(5)].map((_, i) => (
                <FiStar
                  key={i}
                  className={i < Math.floor(product.rating) ? 'fill-current' : ''}
                />
              ))}
            </div>
            <span className="text-gray-600">
              ({product.reviews?.length || 0} تقييم)
            </span>
          </div>

          <div className="text-3xl font-bold text-blue-600 mb-6">
            {product.price.toLocaleString()} ر.س
          </div>

          <p className="text-gray-700 mb-6">{product.description}</p>

          {/* Specifications */}
          {product.specifications && (
            <div className="mb-6">
              <h3 className="text-lg font-semibold mb-3">المواصفات</h3>
              <div className="bg-gray-50 p-4 rounded-lg">
                {product.specifications.material && (
                  <div className="flex justify-between py-2 border-b border-gray-200">
                    <span className="font-medium">المادة:</span>
                    <span>{product.specifications.material}</span>
                  </div>
                )}
                {product.specifications.dimensions && (
                  <div className="flex justify-between py-2 border-b border-gray-200">
                    <span className="font-medium">الأبعاد:</span>
                    <span>
                      {product.specifications.dimensions.width} × {product.specifications.dimensions.height} × {product.specifications.dimensions.depth} سم
                    </span>
                  </div>
                )}
                {product.specifications.color && (
                  <div className="flex justify-between py-2">
                    <span className="font-medium">اللون:</span>
                    <span>{product.specifications.color}</span>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Stock Status */}
          <div className="mb-6">
            {product.stock > 0 ? (
              <span className="text-green-600 font-medium">
                متوفر في المخزون ({product.stock} قطعة)
              </span>
            ) : (
              <span className="text-red-600 font-medium">غير متوفر</span>
            )}
          </div>

          {/* Quantity and Add to Cart */}
          {product.stock > 0 && (
            <div className="flex items-center space-x-4 space-x-reverse mb-6">
              <div className="flex items-center border border-gray-300 rounded-lg">
                <button
                  onClick={() => handleQuantityChange(-1)}
                  className="p-2 hover:bg-gray-100"
                  disabled={quantity <= 1}
                >
                  <FiMinus />
                </button>
                <span className="px-4 py-2 border-x border-gray-300">{quantity}</span>
                <button
                  onClick={() => handleQuantityChange(1)}
                  className="p-2 hover:bg-gray-100"
                  disabled={quantity >= product.stock}
                >
                  <FiPlus />
                </button>
              </div>
              
              <button
                onClick={handleAddToCart}
                className="btn btn-primary flex items-center flex-1"
              >
                <FiShoppingCart className="mr-2" />
                إضافة إلى السلة
              </button>
              
              <button className="btn btn-secondary p-3">
                <FiHeart />
              </button>
            </div>
          )}
        </div>
      </div>

      {/* Reviews Section */}
      <div className="border-t pt-8">
        <h2 className="text-2xl font-bold mb-6">التقييمات والمراجعات</h2>
        
        {/* Add Review Form */}
        {isAuthenticated && (
          <div className="bg-gray-50 p-6 rounded-lg mb-8">
            <h3 className="text-lg font-semibold mb-4">إضافة تقييم</h3>
            <form onSubmit={handleSubmitReview}>
              <div className="mb-4">
                <label className="form-label">التقييم</label>
                <div className="flex space-x-1 space-x-reverse">
                  {[1, 2, 3, 4, 5].map((star) => (
                    <button
                      key={star}
                      type="button"
                      onClick={() => setNewReview(prev => ({ ...prev, rating: star }))}
                      className={`text-2xl ${
                        star <= newReview.rating ? 'text-yellow-400' : 'text-gray-300'
                      }`}
                    >
                      <FiStar className={star <= newReview.rating ? 'fill-current' : ''} />
                    </button>
                  ))}
                </div>
              </div>
              
              <div className="mb-4">
                <label className="form-label">التعليق</label>
                <textarea
                  value={newReview.comment}
                  onChange={(e) => setNewReview(prev => ({ ...prev, comment: e.target.value }))}
                  className="form-textarea"
                  rows="4"
                  placeholder="اكتب تعليقك هنا..."
                  required
                />
              </div>
              
              <button
                type="submit"
                disabled={submittingReview}
                className="btn btn-primary"
              >
                {submittingReview ? 'جاري الإرسال...' : 'إرسال التقييم'}
              </button>
            </form>
          </div>
        )}

        {/* Reviews List */}
        <div className="space-y-6">
          {product.reviews && product.reviews.length > 0 ? (
            product.reviews.map((review, index) => (
              <div key={index} className="border-b border-gray-200 pb-6">
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center">
                    <span className="font-medium">{review.user?.name || 'مستخدم'}</span>
                    <div className="flex items-center text-yellow-400 mr-2">
                      {[...Array(5)].map((_, i) => (
                        <FiStar
                          key={i}
                          className={i < review.rating ? 'fill-current' : ''}
                          size={16}
                        />
                      ))}
                    </div>
                  </div>
                  <span className="text-gray-500 text-sm">
                    {new Date(review.date).toLocaleDateString('ar-SA')}
                  </span>
                </div>
                <p className="text-gray-700">{review.comment}</p>
              </div>
            ))
          ) : (
            <p className="text-gray-500 text-center py-8">لا توجد تقييمات بعد</p>
          )}
        </div>
      </div>
    </div>
  );
}
