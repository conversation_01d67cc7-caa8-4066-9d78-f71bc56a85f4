globalThis.__RSC_MANIFEST=(globalThis.__RSC_MANIFEST||{});globalThis.__RSC_MANIFEST["/products/page"]={"moduleLoading":{"prefix":"/_next/","crossOrigin":null},"ssrModuleMapping":{"(app-pages-browser)/./src/app/page.js":{"*":{"id":"(ssr)/./src/app/page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/Footer.js":{"*":{"id":"(ssr)/./src/components/Footer.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/Navbar.js":{"*":{"id":"(ssr)/./src/components/Navbar.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/context/AuthContext.js":{"*":{"id":"(ssr)/./src/context/AuthContext.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/context/CartContext.js":{"*":{"id":"(ssr)/./src/context/CartContext.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../node_modules/react-toastify/dist/react-toastify.esm.mjs":{"*":{"id":"(ssr)/../node_modules/react-toastify/dist/react-toastify.esm.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../node_modules/next/dist/client/components/app-router.js":{"*":{"id":"(ssr)/../node_modules/next/dist/client/components/app-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(ssr)/../node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(ssr)/../node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(ssr)/../node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../node_modules/next/dist/client/components/not-found-boundary.js":{"*":{"id":"(ssr)/../node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(ssr)/../node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/products/page.js":{"*":{"id":"(ssr)/./src/app/products/page.js","name":"*","chunks":[],"async":false}}},"edgeSSRModuleMapping":{},"clientModules":{"E:\\d1\\client\\src\\app\\page.js":{"id":"(app-pages-browser)/./src/app/page.js","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"E:\\d1\\client\\src\\app\\globals.css":{"id":"(app-pages-browser)/./src/app/globals.css","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"E:\\d1\\client\\src\\components\\Footer.js":{"id":"(app-pages-browser)/./src/components/Footer.js","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"E:\\d1\\client\\src\\components\\Navbar.js":{"id":"(app-pages-browser)/./src/components/Navbar.js","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"E:\\d1\\client\\src\\context\\AuthContext.js":{"id":"(app-pages-browser)/./src/context/AuthContext.js","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"E:\\d1\\client\\src\\context\\CartContext.js":{"id":"(app-pages-browser)/./src/context/CartContext.js","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"E:\\d1\\node_modules\\react-toastify\\dist\\react-toastify.esm.mjs":{"id":"(app-pages-browser)/../node_modules/react-toastify/dist/react-toastify.esm.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"E:\\d1\\node_modules\\react-toastify\\dist\\ReactToastify.css":{"id":"(app-pages-browser)/../node_modules/react-toastify/dist/ReactToastify.css","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"E:\\d1\\node_modules\\next\\dist\\client\\components\\app-router.js":{"id":"(app-pages-browser)/../node_modules/next/dist/client/components/app-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"E:\\d1\\node_modules\\next\\dist\\esm\\client\\components\\app-router.js":{"id":"(app-pages-browser)/../node_modules/next/dist/client/components/app-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"E:\\d1\\node_modules\\next\\dist\\client\\components\\client-page.js":{"id":"(app-pages-browser)/../node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"E:\\d1\\node_modules\\next\\dist\\esm\\client\\components\\client-page.js":{"id":"(app-pages-browser)/../node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"E:\\d1\\node_modules\\next\\dist\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/../node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"E:\\d1\\node_modules\\next\\dist\\esm\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/../node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"E:\\d1\\node_modules\\next\\dist\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/../node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"E:\\d1\\node_modules\\next\\dist\\esm\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/../node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"E:\\d1\\node_modules\\next\\dist\\client\\components\\not-found-boundary.js":{"id":"(app-pages-browser)/../node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"E:\\d1\\node_modules\\next\\dist\\esm\\client\\components\\not-found-boundary.js":{"id":"(app-pages-browser)/../node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"E:\\d1\\node_modules\\next\\dist\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/../node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"E:\\d1\\node_modules\\next\\dist\\esm\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/../node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"E:\\d1\\client\\src\\app\\products\\page.js":{"id":"(app-pages-browser)/./src/app/products/page.js","name":"*","chunks":["app/products/page","static/chunks/app/products/page.js"],"async":false}},"entryCSSFiles":{"E:\\d1\\client\\src\\":[],"E:\\d1\\client\\src\\app\\page":[],"E:\\d1\\client\\src\\app\\layout":["static/css/app/layout.css"],"E:\\d1\\client\\src\\app\\products\\page":[]}}