{"name": "server", "version": "1.0.0", "description": "Express server for furniture store", "main": "index.js", "type": "module", "scripts": {"start": "node index.js", "dev": "nodemon index.js"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "dotenv": "^16.3.1", "mongoose": "^8.0.0", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "multer": "^1.4.5-lts.1"}, "devDependencies": {"nodemon": "^3.0.1"}}