{"name": "server", "version": "1.0.0", "description": "Express server for furniture store", "main": "index.js", "scripts": {"start": "node index.js", "dev": "nodemon index.js"}, "dependencies": {"bcryptjs": "^2.4.3", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^4.21.2", "jsonwebtoken": "^9.0.2", "mongoose": "^8.15.1", "multer": "^1.4.5-lts.1"}, "devDependencies": {"nodemon": "^3.0.1"}, "keywords": [], "author": "", "license": "ISC"}